/**
 * Storage Service Module
 * 
 * Provides a unified interface for browser storage (localStorage, sessionStorage)
 * with automatic serialization, expiration support, and error handling.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

class StorageService {
    constructor() {
        this.prefix = 'app_';
        this.isAvailable = this.checkAvailability();
    }
    
    /**
     * Check if storage is available
     * @returns {boolean} Storage availability
     */
    checkAvailability() {
        try {
            const test = '__storage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            console.warn('Storage not available:', e);
            return false;
        }
    }
    
    /**
     * Get prefixed key
     * @param {string} key - Original key
     * @returns {string} Prefixed key
     */
    getPrefixedKey(key) {
        return `${this.prefix}${key}`;
    }
    
    /**
     * Set item in localStorage
     * @param {string} key - Storage key
     * @param {*} value - Value to store
     * @param {number} ttl - Time to live in milliseconds (optional)
     * @returns {boolean} Success status
     */
    setLocal(key, value, ttl = null) {
        if (!this.isAvailable) return false;
        
        try {
            const prefixedKey = this.getPrefixedKey(key);
            const item = {
                value,
                timestamp: Date.now(),
                ttl
            };
            
            localStorage.setItem(prefixedKey, JSON.stringify(item));
            return true;
        } catch (e) {
            console.error('Failed to set localStorage item:', e);
            return false;
        }
    }
    
    /**
     * Get item from localStorage
     * @param {string} key - Storage key
     * @param {*} defaultValue - Default value if not found
     * @returns {*} Stored value or default
     */
    getLocal(key, defaultValue = null) {
        if (!this.isAvailable) return defaultValue;
        
        try {
            const prefixedKey = this.getPrefixedKey(key);
            const stored = localStorage.getItem(prefixedKey);
            
            if (!stored) return defaultValue;
            
            const item = JSON.parse(stored);
            
            // Check if item has expired
            if (item.ttl && Date.now() - item.timestamp > item.ttl) {
                this.removeLocal(key);
                return defaultValue;
            }
            
            return item.value;
        } catch (e) {
            console.error('Failed to get localStorage item:', e);
            return defaultValue;
        }
    }
    
    /**
     * Remove item from localStorage
     * @param {string} key - Storage key
     * @returns {boolean} Success status
     */
    removeLocal(key) {
        if (!this.isAvailable) return false;
        
        try {
            const prefixedKey = this.getPrefixedKey(key);
            localStorage.removeItem(prefixedKey);
            return true;
        } catch (e) {
            console.error('Failed to remove localStorage item:', e);
            return false;
        }
    }
    
    /**
     * Set item in sessionStorage
     * @param {string} key - Storage key
     * @param {*} value - Value to store
     * @returns {boolean} Success status
     */
    setSession(key, value) {
        if (!this.isAvailable) return false;
        
        try {
            const prefixedKey = this.getPrefixedKey(key);
            const item = {
                value,
                timestamp: Date.now()
            };
            
            sessionStorage.setItem(prefixedKey, JSON.stringify(item));
            return true;
        } catch (e) {
            console.error('Failed to set sessionStorage item:', e);
            return false;
        }
    }
    
    /**
     * Get item from sessionStorage
     * @param {string} key - Storage key
     * @param {*} defaultValue - Default value if not found
     * @returns {*} Stored value or default
     */
    getSession(key, defaultValue = null) {
        if (!this.isAvailable) return defaultValue;
        
        try {
            const prefixedKey = this.getPrefixedKey(key);
            const stored = sessionStorage.getItem(prefixedKey);
            
            if (!stored) return defaultValue;
            
            const item = JSON.parse(stored);
            return item.value;
        } catch (e) {
            console.error('Failed to get sessionStorage item:', e);
            return defaultValue;
        }
    }
    
    /**
     * Remove item from sessionStorage
     * @param {string} key - Storage key
     * @returns {boolean} Success status
     */
    removeSession(key) {
        if (!this.isAvailable) return false;
        
        try {
            const prefixedKey = this.getPrefixedKey(key);
            sessionStorage.removeItem(prefixedKey);
            return true;
        } catch (e) {
            console.error('Failed to remove sessionStorage item:', e);
            return false;
        }
    }
    
    /**
     * Clear all app-related items from localStorage
     * @returns {boolean} Success status
     */
    clearLocal() {
        if (!this.isAvailable) return false;
        
        try {
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith(this.prefix)) {
                    localStorage.removeItem(key);
                }
            });
            return true;
        } catch (e) {
            console.error('Failed to clear localStorage:', e);
            return false;
        }
    }
    
    /**
     * Clear all app-related items from sessionStorage
     * @returns {boolean} Success status
     */
    clearSession() {
        if (!this.isAvailable) return false;
        
        try {
            const keys = Object.keys(sessionStorage);
            keys.forEach(key => {
                if (key.startsWith(this.prefix)) {
                    sessionStorage.removeItem(key);
                }
            });
            return true;
        } catch (e) {
            console.error('Failed to clear sessionStorage:', e);
            return false;
        }
    }
    
    /**
     * Get storage usage information
     * @returns {Object} Storage usage stats
     */
    getUsage() {
        if (!this.isAvailable) {
            return { available: false };
        }
        
        try {
            let localSize = 0;
            let sessionSize = 0;
            let appLocalItems = 0;
            let appSessionItems = 0;
            
            // Calculate localStorage usage
            Object.keys(localStorage).forEach(key => {
                const value = localStorage.getItem(key);
                localSize += key.length + (value ? value.length : 0);
                if (key.startsWith(this.prefix)) {
                    appLocalItems++;
                }
            });
            
            // Calculate sessionStorage usage
            Object.keys(sessionStorage).forEach(key => {
                const value = sessionStorage.getItem(key);
                sessionSize += key.length + (value ? value.length : 0);
                if (key.startsWith(this.prefix)) {
                    appSessionItems++;
                }
            });
            
            return {
                available: true,
                localStorage: {
                    size: localSize,
                    appItems: appLocalItems
                },
                sessionStorage: {
                    size: sessionSize,
                    appItems: appSessionItems
                }
            };
        } catch (e) {
            console.error('Failed to get storage usage:', e);
            return { available: false, error: e.message };
        }
    }
}

// Create and export singleton instance
export const storage = new StorageService();
