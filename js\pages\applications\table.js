/**
 * Applications Table Module
 * 
 * Handles table display, pagination, search, and data management
 * for the applications page.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { events } from '../../core/events.js';
import { state } from '../../core/state.js';
import { getApplications, deleteApplication } from '../../services/api.js';

export class ApplicationTable {
    constructor(containerId, options = {}) {
        this.container = typeof containerId === 'string' ? document.getElementById(containerId) : containerId;
        this.options = {
            onEdit: null,
            onDelete: null,
            onSelectionChange: null,
            ...options
        };

        this.table = null;
        this.pagination = null;
        this.search = null;
        this.data = [];

        this.init();
    }

    /**
     * Initialize the table component
     */
    init() {
        // For applications page, we work with the existing HTML structure
        // instead of requiring a specific container
        this.setupComponents();
        this.setupEventListeners();

        console.log('Applications table initialized');
    }
    
    /**
     * Set up table components
     */
    setupComponents() {
        // Work with existing HTML structure instead of creating new components
        // The table, search, and pagination are already in the HTML

        // Set up items per page selector
        this.setupItemsPerPageSelector();

        console.log('Applications table components set up with existing HTML structure');
    }
    
    /**
     * Set up items per page selector
     */
    setupItemsPerPageSelector() {
        const itemsPerPageSelect = document.getElementById('itemsPerPage');
        if (itemsPerPageSelect) {
            itemsPerPageSelect.addEventListener('change', (e) => {
                const itemsPerPage = parseInt(e.target.value);
                state.update({
                    'applications.itemsPerPage': itemsPerPage,
                    'applications.currentPage': 1
                });
                this.loadData();
            });
        }
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Search input with debouncing
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    state.update({
                        'applications.searchQuery': e.target.value.trim(),
                        'applications.currentPage': 1
                    });
                    this.loadData();
                }, 300);
            });
        }

        // Clear search button
        const clearSearchBtn = document.querySelector('[data-action="clear-search"]');
        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', () => {
                if (searchInput) {
                    searchInput.value = '';
                }
                state.update({
                    'applications.searchQuery': '',
                    'applications.currentPage': 1
                });
                this.loadData();
            });
        }

        // Refresh button
        const refreshBtn = document.querySelector('[data-action="refresh-table"]');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadData();
            });
        }

        // Table row actions (edit/delete buttons)
        document.addEventListener('click', (e) => {
            const editBtn = e.target.closest('[data-action="edit-app"]');
            const deleteBtn = e.target.closest('[data-action="delete-app"]');

            if (editBtn) {
                e.preventDefault();
                const id = parseInt(editBtn.getAttribute('data-id'));
                if (this.options.onEdit) {
                    this.options.onEdit(id);
                }
            } else if (deleteBtn) {
                e.preventDefault();
                const id = parseInt(deleteBtn.getAttribute('data-id'));
                if (this.options.onDelete) {
                    this.options.onDelete(id);
                }
            }
        });
    }
    
    /**
     * Load table data from API
     */
    async loadData() {
        try {
            // Set loading state
            state.set('applications.isLoading', true);
            this.showLoading(true);
            
            // Get current state
            const currentPage = state.get('applications.currentPage') || 1;
            const itemsPerPage = state.get('applications.itemsPerPage') || 10;
            const searchQuery = state.get('applications.searchQuery') || '';
            
            // Fetch data
            const response = await getApplications(currentPage, itemsPerPage, searchQuery);
            
            // Update state
            state.update({
                'applications.totalItems': response.total,
                'applications.totalPages': response.pages
            });
            
            // Update components
            this.data = response.applications;
            this.updateTable(response.applications);
            this.updatePagination(response);
            this.updateEmptyState(response.applications.length === 0);
            
            events.emit('applications:loaded', response);
            
        } catch (error) {
            console.error('Error loading applications:', error);
            this.showError('Failed to load applications. Please check your connection and try again.');
            
            // Show empty state on error
            this.updateTable([]);
            this.updateEmptyState(true);
            
        } finally {
            state.set('applications.isLoading', false);
            this.showLoading(false);
        }
    }
    
    /**
     * Update table with data
     * @param {Array} applications - Applications data
     */
    updateTable(applications) {
        const tbody = document.getElementById('tableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        applications.forEach((app, index) => {
            const currentPage = state.get('applications.currentPage') || 1;
            const itemsPerPage = state.get('applications.itemsPerPage') || 10;
            const globalIndex = (currentPage - 1) * itemsPerPage + index + 1;

            const row = document.createElement('tr');
            row.className = 'table-row';
            row.dataset.id = app.id;
            row.style.cursor = 'pointer';

            // Truncate publisher and description
            const truncatedPublisher = app.publisher.length > 20
                ? app.publisher.substring(0, 20) + '...'
                : app.publisher;

            const truncatedDescription = app.description && app.description.length > 50
                ? app.description.substring(0, 50) + '...'
                : app.description;

            row.innerHTML = `
                <td>${globalIndex}</td>
                <td>
                    <div>
                        <strong>${app.app_name}</strong>
                        ${app.description ? `<br><small class="text-muted">${truncatedDescription}</small>` : ''}
                    </div>
                </td>
                <td>${app.current_version}</td>
                <td title="${app.publisher}">${truncatedPublisher}</td>
                <td class="text-end">
                    <button type="button" class="btn btn-outline-primary btn-sm"
                            data-action="edit-app" data-id="${app.id}" title="Edit">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm ms-1"
                            data-action="delete-app" data-id="${app.id}" title="Delete">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;

            // Add click event for row selection
            row.addEventListener('click', (e) => {
                // Don't select row if clicking on action buttons
                if (!e.target.closest('button')) {
                    this.selectRow(row, app.id);
                }
            });

            tbody.appendChild(row);
        });
    }

    /**
     * Update pagination component
     * @param {Object} response - API response with pagination data
     */
    updatePagination(response) {
        // Update pagination info
        const paginationInfo = document.getElementById('paginationInfo');
        if (paginationInfo) {
            const start = response.total === 0 ? 0 : (response.page - 1) * (state.get('applications.itemsPerPage') || 10) + 1;
            const end = Math.min(response.page * (state.get('applications.itemsPerPage') || 10), response.total);
            const searchQuery = state.get('applications.searchQuery') || '';

            paginationInfo.textContent =
                `Showing ${start} to ${end} of ${response.total} entries${searchQuery ? ' (filtered)' : ''}`;
        }

        // Update pagination controls
        const paginationControls = document.getElementById('paginationControls');
        if (paginationControls) {
            this.renderPaginationControls(paginationControls, response);
        }
    }
    
    /**
     * Update empty state visibility
     * @param {boolean} isEmpty - Whether data is empty
     */
    updateEmptyState(isEmpty) {
        const emptyState = document.getElementById('emptyState');
        if (emptyState) {
            emptyState.style.display = isEmpty ? 'block' : 'none';
        }
    }
    
    /**
     * Show/hide loading state
     * @param {boolean} loading - Loading state
     */
    showLoading(loading) {
        const loadingSpinner = document.getElementById('loadingSpinner');
        const tableContainer = document.querySelector('.table-responsive');
        
        if (loadingSpinner) {
            loadingSpinner.style.display = loading ? 'block' : 'none';
        }
        
        if (tableContainer) {
            tableContainer.style.display = loading ? 'none' : 'block';
        }
    }
    
    /**
     * Show error message
     * @param {string} message - Error message
     */
    showError(message) {
        // For now use alert, can be replaced with toast notifications
        alert(message);
    }
    
    /**
     * Delete application
     * @param {number} id - Application ID
     */
    async deleteApplication(id) {
        try {
            await deleteApplication(id);
            events.emit('applications:deleted', id);
            
            // Reload data
            await this.loadData();
            
        } catch (error) {
            console.error('Error deleting application:', error);
            this.showError('Failed to delete application. Please try again.');
            throw error;
        }
    }
    
    /**
     * Select a table row
     * @param {HTMLElement} row - Row element
     * @param {number} id - Application ID
     */
    selectRow(row, id) {
        // Clear previous selection
        document.querySelectorAll('#tableBody tr.table-active').forEach(r => {
            r.classList.remove('table-active');
        });

        // Select current row
        row.classList.add('table-active');

        // Update state
        state.set('applications.selectedRowId', id);

        // Notify selection change
        if (this.options.onSelectionChange) {
            this.options.onSelectionChange({
                selectedIds: [id],
                selectedData: [this.data.find(app => app.id == id)]
            });
        }
    }

    /**
     * Clear table selection
     */
    clearSelection() {
        document.querySelectorAll('#tableBody tr.table-active').forEach(row => {
            row.classList.remove('table-active');
        });
        state.set('applications.selectedRowId', null);
    }

    /**
     * Render pagination controls
     * @param {HTMLElement} container - Pagination container
     * @param {Object} response - API response
     */
    renderPaginationControls(container, response) {
        const { page, pages } = response;

        container.innerHTML = '';

        if (pages <= 1) return;

        const nav = document.createElement('nav');
        const ul = document.createElement('ul');
        ul.className = 'pagination mb-0';

        // Previous button
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${page === 1 ? 'disabled' : ''}`;
        prevLi.innerHTML = `<a class="page-link" href="#" data-page="${page - 1}">Previous</a>`;
        ul.appendChild(prevLi);

        // Page numbers
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(pages, page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const li = document.createElement('li');
            li.className = `page-item ${i === page ? 'active' : ''}`;
            li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
            ul.appendChild(li);
        }

        // Next button
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${page === pages ? 'disabled' : ''}`;
        nextLi.innerHTML = `<a class="page-link" href="#" data-page="${page + 1}">Next</a>`;
        ul.appendChild(nextLi);

        nav.appendChild(ul);
        container.appendChild(nav);

        // Add click handlers
        ul.addEventListener('click', (e) => {
            const link = e.target.closest('[data-page]');
            if (link && !link.closest('.disabled')) {
                e.preventDefault();
                const newPage = parseInt(link.getAttribute('data-page'));
                state.set('applications.currentPage', newPage);
                this.loadData();
            }
        });
    }
    
    /**
     * Update from state changes
     * @param {Object} applicationsState - Applications state
     */
    updateFromState(applicationsState) {
        // Update loading state
        if (applicationsState.isLoading !== undefined) {
            this.showLoading(applicationsState.isLoading);
        }
        
        // Update search query
        if (this.search && applicationsState.searchQuery !== undefined) {
            this.search.setQuery(applicationsState.searchQuery);
        }
    }
    
    /**
     * Destroy the table component
     */
    destroy() {
        if (this.table) {
            this.table.destroy();
        }
        
        if (this.pagination) {
            this.pagination.destroy();
        }
        
        if (this.search) {
            this.search.destroy();
        }
        
        console.log('Applications table destroyed');
    }
}
