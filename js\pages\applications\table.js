/**
 * Application Table Component
 * 
 * Handles application data display, pagination, search, and CRUD operations
 * Provides event-driven interface for table interactions
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { applicationApi } from '../../services/api.js';
import { EventEmitter } from '../../core/events.js';
import { formatDate, debounce } from '../../utils/helpers.js';
import { AppStorage } from '../../services/storage.js';

/**
 * Application Table Component Class
 * Manages table data, pagination, search, and user interactions
 */
export class ApplicationTable extends EventEmitter {
    constructor(container) {
        super();
        this.container = container;
        this.table = null;
        this.data = [];
        this.filteredData = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.totalItems = 0;
        this.searchQuery = '';
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.initialized = false;
        
        // Debounced search function
        this.debouncedSearch = debounce((query) => this.performSearch(query), 300);
    }
    
    /**
     * Initialize the table component
     */
    async init() {
        if (this.initialized) return;
        
        try {
            console.log('Initializing ApplicationTable component...');
            console.log('Container:', this.container);
            console.log('Container innerHTML before:', this.container.innerHTML);
            
            // Load saved settings
            this.loadSettings();
            
            // Always create table HTML to ensure proper structure
            console.log('Creating table HTML...');
            await this.createTableHTML();
            console.log('Table HTML created. Container innerHTML after:', this.container.innerHTML);
            
            // Get table element
            console.log('Looking for table element...');
            this.table = this.container.querySelector('table');
            console.log('Table element found:', this.table);
            
            if (!this.table) {
                console.error('Available elements in container:', this.container.querySelectorAll('*'));
                throw new Error('Table element not found in container');
            }
            
            // Setup event listeners
            this.setupEventListeners();
            
            this.initialized = true;
            console.log('ApplicationTable component initialized successfully');
            
        } catch (error) {
            console.error('Error initializing ApplicationTable:', error);
            console.error('Container state:', this.container);
            console.error('Container innerHTML:', this.container.innerHTML);
            throw error;
        }
    }
    
    /**
     * Create table HTML structure
     */
    async createTableHTML() {
        this.container.innerHTML = `
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title mb-0">Applications</h5>
                        </div>
                        <div class="col-auto">
                            <div class="d-flex gap-2">
                                <div class="input-group" style="width: 250px;">
                                    <input type="text" class="form-control" id="searchInput" 
                                           placeholder="Search applications..." value="${this.searchQuery}">
                                    <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                        <i class="bi bi-x"></i>
                                    </button>
                                </div>
                                <button class="btn btn-primary" data-action="create-application">
                                    <i class="bi bi-plus"></i> Add New
                                </button>
                                <button class="btn btn-outline-secondary" data-action="refresh-applications">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="sortable" data-column="app_name">
                                        Application Name
                                        <i class="bi bi-arrow-down-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-column="app_type">
                                        Type
                                        <i class="bi bi-arrow-down-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-column="current_version">
                                        Version
                                        <i class="bi bi-arrow-down-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-column="publisher">
                                        Publisher
                                        <i class="bi bi-arrow-down-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-column="released_date">
                                        Released
                                        <i class="bi bi-arrow-down-up sort-icon"></i>
                                    </th>
                                    <th class="sortable" data-column="registered_date">
                                        Registered
                                        <i class="bi bi-arrow-down-up sort-icon"></i>
                                    </th>
                                    <th width="120">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="tableBody">
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="d-flex align-items-center gap-2">
                                <span class="text-muted">Show:</span>
                                <select class="form-select form-select-sm" id="itemsPerPageSelect" style="width: auto;">
                                    <option value="5">5</option>
                                    <option value="10" selected>10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                </select>
                                <span class="text-muted">entries</span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <span class="text-muted" id="paginationInfo">Showing 0 to 0 of 0 entries</span>
                        </div>
                        <div class="col-auto">
                            <nav>
                                <ul class="pagination pagination-sm mb-0" id="pagination">
                                    <!-- Pagination will be generated here -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Search input
        const searchInput = this.container.querySelector('#searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value;
                this.debouncedSearch(this.searchQuery);
            });
        }
        
        // Clear search button
        const clearSearch = this.container.querySelector('#clearSearch');
        if (clearSearch) {
            clearSearch.addEventListener('click', () => {
                searchInput.value = '';
                this.searchQuery = '';
                this.performSearch('');
            });
        }
        
        // Items per page selector
        const itemsPerPageSelect = this.container.querySelector('#itemsPerPageSelect');
        if (itemsPerPageSelect) {
            itemsPerPageSelect.value = this.itemsPerPage.toString();
            itemsPerPageSelect.addEventListener('change', (e) => {
                this.itemsPerPage = parseInt(e.target.value);
                this.currentPage = 1;
                this.saveSettings();
                this.renderTable();
                this.renderPagination();
            });
        }
        
        // Table sorting
        this.container.addEventListener('click', (e) => {
            const sortable = e.target.closest('.sortable');
            if (sortable) {
                const column = sortable.getAttribute('data-column');
                this.handleSort(column);
            }
        });
        
        // Action buttons
        this.container.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="edit"]') || e.target.closest('[data-action="edit"]')) {
                e.preventDefault();
                const button = e.target.closest('[data-action="edit"]');
                const id = parseInt(button.getAttribute('data-id'));
                const application = this.data.find(app => app.id === id);
                if (application) {
                    this.emit('edit', application);
                }
            }
            
            if (e.target.matches('[data-action="delete"]') || e.target.closest('[data-action="delete"]')) {
                e.preventDefault();
                const button = e.target.closest('[data-action="delete"]');
                const id = parseInt(button.getAttribute('data-id'));
                const application = this.data.find(app => app.id === id);
                if (application) {
                    this.emit('delete', application);
                }
            }
        });
        
        // Pagination
        this.container.addEventListener('click', (e) => {
            if (e.target.matches('[data-page]')) {
                e.preventDefault();
                const page = parseInt(e.target.getAttribute('data-page'));
                this.goToPage(page);
            }
        });
    }
    
    /**
     * Load data from API
     */
    async loadData() {
        try {
            console.log('Loading applications data...');
            this.showLoading();
            
            // Fetch data from API
            const response = await applicationApi.getAll(this.currentPage, this.itemsPerPage, this.searchQuery);
            
            this.data = response.applications || [];
            this.totalItems = response.total || 0;
            
            // Apply client-side filtering if needed
            this.applyFilters();
            
            // Render table and pagination
            this.renderTable();
            this.renderPagination();
            
            this.emit('dataLoaded', {
                data: this.data,
                total: this.totalItems,
                page: this.currentPage
            });
            
            console.log(`Loaded ${this.data.length} applications`);
            
        } catch (error) {
            console.error('Error loading applications:', error);
            this.showError('Failed to load applications. Please try again.');
            this.emit('loadError', error);
        }
    }
    
    /**
     * Perform search
     * @param {string} query - Search query
     */
    async performSearch(query) {
        this.searchQuery = query;
        this.currentPage = 1;
        
        // Save search to history
        if (query.trim()) {
            AppStorage.addToSearchHistory('applications', query.trim());
        }
        
        await this.loadData();
        this.emit('search', query);
    }
    
    /**
     * Apply client-side filters
     */
    applyFilters() {
        this.filteredData = [...this.data];
        
        // Apply sorting
        if (this.sortColumn) {
            this.filteredData.sort((a, b) => {
                let aVal = a[this.sortColumn] || '';
                let bVal = b[this.sortColumn] || '';
                
                // Handle date sorting
                if (this.sortColumn.includes('date')) {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                }
                
                // Handle string sorting
                if (typeof aVal === 'string') {
                    aVal = aVal.toLowerCase();
                    bVal = bVal.toLowerCase();
                }
                
                if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
                return 0;
            });
        }
    }
    
    /**
     * Handle column sorting
     * @param {string} column - Column to sort by
     */
    handleSort(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }
        
        this.saveSettings();
        this.applyFilters();
        this.renderTable();
        this.updateSortIcons();
    }
    
    /**
     * Update sort icons
     */
    updateSortIcons() {
        // Reset all sort icons
        this.container.querySelectorAll('.sort-icon').forEach(icon => {
            icon.className = 'bi bi-arrow-down-up sort-icon';
        });
        
        // Update active sort icon
        if (this.sortColumn) {
            const activeHeader = this.container.querySelector(`[data-column="${this.sortColumn}"] .sort-icon`);
            if (activeHeader) {
                activeHeader.className = `bi bi-arrow-${this.sortDirection === 'asc' ? 'up' : 'down'} sort-icon`;
            }
        }
    }
    
    /**
     * Render table data
     */
    renderTable() {
        const tbody = this.container.querySelector('#tableBody');
        if (!tbody) return;
        
        if (this.filteredData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4 text-muted">
                        ${this.searchQuery ? 'No applications found matching your search.' : 'No applications found.'}
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = this.filteredData.map(app => `
            <tr>
                <td>
                    <div class="fw-medium">${this.escapeHtml(app.app_name)}</div>
                    ${app.description ? `<small class="text-muted">${this.escapeHtml(app.description.substring(0, 50))}${app.description.length > 50 ? '...' : ''}</small>` : ''}
                </td>
                <td>
                    <span class="badge bg-secondary">${this.escapeHtml(app.app_type)}</span>
                </td>
                <td>
                    <code>${this.escapeHtml(app.current_version)}</code>
                </td>
                <td>${this.escapeHtml(app.publisher)}</td>
                <td>
                    <small>${formatDate(app.released_date, 'short')}</small>
                </td>
                <td>
                    <small>${formatDate(app.registered_date, 'short')}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" data-action="edit" data-id="${app.id}" title="Edit">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-danger" data-action="delete" data-id="${app.id}" title="Delete">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    /**
     * Render pagination
     */
    renderPagination() {
        const pagination = this.container.querySelector('#pagination');
        const paginationInfo = this.container.querySelector('#paginationInfo');
        
        if (!pagination || !paginationInfo) return;
        
        const totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        const startItem = this.totalItems === 0 ? 0 : (this.currentPage - 1) * this.itemsPerPage + 1;
        const endItem = Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
        
        // Update pagination info
        paginationInfo.textContent = `Showing ${startItem} to ${endItem} of ${this.totalItems} entries`;
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // Previous button
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.currentPage - 1}">Previous</a>
            </li>
        `;
        
        // Page numbers
        const maxVisiblePages = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
        
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }
        
        if (startPage > 1) {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>`;
            if (startPage > 2) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `;
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a></li>`;
        }
        
        // Next button
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.currentPage + 1}">Next</a>
            </li>
        `;
        
        pagination.innerHTML = paginationHTML;
    }
    
    /**
     * Go to specific page
     * @param {number} page - Page number
     */
    async goToPage(page) {
        if (page < 1 || page === this.currentPage) return;
        
        this.currentPage = page;
        this.saveSettings();
        await this.loadData();
        this.emit('pageChange', page);
    }
    
    /**
     * Search applications
     * @param {string} query - Search query
     */
    async search(query) {
        await this.performSearch(query);
    }
    
    /**
     * Show loading state
     */
    showLoading() {
        const tbody = this.container.querySelector('#tableBody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </td>
                </tr>
            `;
        }
    }
    
    /**
     * Show error message
     * @param {string} message - Error message
     */
    showError(message) {
        const tbody = this.container.querySelector('#tableBody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4 text-danger">
                        <i class="bi bi-exclamation-triangle"></i> ${message}
                    </td>
                </tr>
            `;
        }
    }
    
    /**
     * Escape HTML to prevent XSS
     * @param {string} text - Text to escape
     * @returns {string} Escaped text
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    /**
     * Load settings from storage
     */
    loadSettings() {
        const settings = AppStorage.getTableSettings('applications');
        this.itemsPerPage = settings.itemsPerPage || 10;
        this.sortColumn = settings.sortColumn || null;
        this.sortDirection = settings.sortDirection || 'asc';
    }
    
    /**
     * Save settings to storage
     */
    saveSettings() {
        AppStorage.setTableSettings('applications', {
            itemsPerPage: this.itemsPerPage,
            sortColumn: this.sortColumn,
            sortDirection: this.sortDirection
        });
    }
    
    /**
     * Get current table data
     * @returns {Array} Current data
     */
    getData() {
        return this.filteredData;
    }
    
    /**
     * Refresh table data
     */
    async refresh() {
        await this.loadData();
    }
    
    /**
     * Destroy the component
     */
    async destroy() {
        try {
            console.log('Destroying ApplicationTable component...');
            
            // Clear container
            if (this.container) {
                this.container.innerHTML = '';
            }
            
            // Reset state
            this.table = null;
            this.data = [];
            this.filteredData = [];
            this.initialized = false;
            
            // Remove all event listeners
            this.removeAllListeners();
            
            console.log('ApplicationTable component destroyed');
            
        } catch (error) {
            console.error('Error destroying ApplicationTable:', error);
        }
    }
}
