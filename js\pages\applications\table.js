/**
 * Applications Table Module
 * 
 * Handles table display, pagination, search, and data management
 * for the applications page.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { events } from '../../core/events.js';
import { state } from '../../core/state.js';
import { Table } from '../../components/table.js';
import { Pagination } from '../../components/pagination.js';
import { Search } from '../../components/search.js';
import { getApplications, deleteApplication } from '../../services/api.js';

export class ApplicationTable {
    constructor(containerId, options = {}) {
        this.container = typeof containerId === 'string' ? document.getElementById(containerId) : containerId;
        this.options = {
            onEdit: null,
            onDelete: null,
            onSelectionChange: null,
            ...options
        };
        
        this.table = null;
        this.pagination = null;
        this.search = null;
        this.data = [];
        
        this.init();
    }
    
    /**
     * Initialize the table component
     */
    init() {
        if (!this.container) {
            console.error('Table container not found');
            return;
        }
        
        this.setupComponents();
        this.setupEventListeners();
        
        console.log('Applications table initialized');
    }
    
    /**
     * Set up table components
     */
    setupComponents() {
        // Set up search component
        const searchContainer = document.getElementById('searchContainer');
        if (searchContainer) {
            this.search = new Search(searchContainer, {
                placeholder: 'Search applications...',
                debounceDelay: 300,
                showClearButton: true
            });
        }
        
        // Set up table component
        const tableContainer = document.getElementById('tableContainer');
        if (tableContainer) {
            this.table = new Table(tableContainer, {
                selectable: true,
                emptyMessage: 'No applications found'
            });
            
            // Define table columns
            this.table.setColumns([
                {
                    key: 'index',
                    title: '#',
                    width: '60px',
                    render: (data, index) => {
                        const currentPage = state.get('applications.currentPage') || 1;
                        const itemsPerPage = state.get('applications.itemsPerPage') || 10;
                        return (currentPage - 1) * itemsPerPage + index + 1;
                    }
                },
                {
                    key: 'app_name',
                    title: 'Application',
                    render: (data) => {
                        const description = data.description && data.description.length > 50
                            ? data.description.substring(0, 50) + '...'
                            : data.description;
                        
                        return `
                            <div>
                                <strong>${data.app_name}</strong>
                                ${description ? `<br><small class="text-muted">${description}</small>` : ''}
                            </div>
                        `;
                    }
                },
                {
                    key: 'current_version',
                    title: 'Version',
                    width: '100px'
                },
                {
                    key: 'publisher',
                    title: 'Publisher',
                    width: '150px',
                    render: (data) => {
                        const truncated = data.publisher.length > 20
                            ? data.publisher.substring(0, 20) + '...'
                            : data.publisher;
                        return `<span title="${data.publisher}">${truncated}</span>`;
                    }
                },
                {
                    key: 'actions',
                    title: 'Actions',
                    width: '100px',
                    className: 'text-end',
                    render: (data) => {
                        return `
                            <button type="button" class="btn btn-outline-primary btn-sm" 
                                    data-action="edit" data-id="${data.id}" title="Edit">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm ms-1" 
                                    data-action="delete" data-id="${data.id}" title="Delete">
                                <i class="bi bi-trash"></i>
                            </button>
                        `;
                    }
                }
            ]);
        }
        
        // Set up pagination component
        const paginationContainer = document.getElementById('paginationContainer');
        if (paginationContainer) {
            this.pagination = new Pagination(paginationContainer, {
                showInfo: true,
                maxVisiblePages: 5
            });
        }
        
        // Set up items per page selector
        this.setupItemsPerPageSelector();
    }
    
    /**
     * Set up items per page selector
     */
    setupItemsPerPageSelector() {
        const itemsPerPageSelect = document.getElementById('itemsPerPage');
        if (itemsPerPageSelect) {
            itemsPerPageSelect.addEventListener('change', (e) => {
                const itemsPerPage = parseInt(e.target.value);
                state.update({
                    'applications.itemsPerPage': itemsPerPage,
                    'applications.currentPage': 1
                });
                this.loadData();
            });
        }
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Table action events
        events.on('table:action:edit', (data) => {
            if (this.options.onEdit) {
                this.options.onEdit(parseInt(data.id));
            }
        });
        
        events.on('table:action:delete', (data) => {
            if (this.options.onDelete) {
                this.options.onDelete(parseInt(data.id));
            }
        });
        
        // Table selection events
        events.on('table:selection:changed', (data) => {
            if (this.options.onSelectionChange) {
                this.options.onSelectionChange(data);
            }
        });
        
        // Search events
        events.on('search:performed', (searchData) => {
            state.update({
                'applications.searchQuery': searchData.query,
                'applications.currentPage': 1
            });
            this.loadData();
        });
        
        // Pagination events
        events.on('pagination:changed', (paginationData) => {
            state.set('applications.currentPage', paginationData.currentPage);
            this.loadData();
        });
        
        // Refresh button
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadData();
            });
        }
    }
    
    /**
     * Load table data from API
     */
    async loadData() {
        try {
            // Set loading state
            state.set('applications.isLoading', true);
            this.showLoading(true);
            
            // Get current state
            const currentPage = state.get('applications.currentPage') || 1;
            const itemsPerPage = state.get('applications.itemsPerPage') || 10;
            const searchQuery = state.get('applications.searchQuery') || '';
            
            // Fetch data
            const response = await getApplications(currentPage, itemsPerPage, searchQuery);
            
            // Update state
            state.update({
                'applications.totalItems': response.total,
                'applications.totalPages': response.pages
            });
            
            // Update components
            this.data = response.applications;
            this.updateTable(response.applications);
            this.updatePagination(response);
            this.updateEmptyState(response.applications.length === 0);
            
            events.emit('applications:loaded', response);
            
        } catch (error) {
            console.error('Error loading applications:', error);
            this.showError('Failed to load applications. Please check your connection and try again.');
            
            // Show empty state on error
            this.updateTable([]);
            this.updateEmptyState(true);
            
        } finally {
            state.set('applications.isLoading', false);
            this.showLoading(false);
        }
    }
    
    /**
     * Update table with data
     * @param {Array} applications - Applications data
     */
    updateTable(applications) {
        if (this.table) {
            this.table.setData(applications);
        }
    }
    
    /**
     * Update pagination component
     * @param {Object} response - API response with pagination data
     */
    updatePagination(response) {
        if (this.pagination) {
            this.pagination.update({
                currentPage: response.page,
                totalPages: response.pages,
                totalItems: response.total,
                itemsPerPage: state.get('applications.itemsPerPage') || 10
            });
        }
    }
    
    /**
     * Update empty state visibility
     * @param {boolean} isEmpty - Whether data is empty
     */
    updateEmptyState(isEmpty) {
        const emptyState = document.getElementById('emptyState');
        if (emptyState) {
            emptyState.style.display = isEmpty ? 'block' : 'none';
        }
    }
    
    /**
     * Show/hide loading state
     * @param {boolean} loading - Loading state
     */
    showLoading(loading) {
        const loadingSpinner = document.getElementById('loadingSpinner');
        const tableContainer = document.querySelector('.table-responsive');
        
        if (loadingSpinner) {
            loadingSpinner.style.display = loading ? 'block' : 'none';
        }
        
        if (tableContainer) {
            tableContainer.style.display = loading ? 'none' : 'block';
        }
    }
    
    /**
     * Show error message
     * @param {string} message - Error message
     */
    showError(message) {
        // For now use alert, can be replaced with toast notifications
        alert(message);
    }
    
    /**
     * Delete application
     * @param {number} id - Application ID
     */
    async deleteApplication(id) {
        try {
            await deleteApplication(id);
            events.emit('applications:deleted', id);
            
            // Reload data
            await this.loadData();
            
        } catch (error) {
            console.error('Error deleting application:', error);
            this.showError('Failed to delete application. Please try again.');
            throw error;
        }
    }
    
    /**
     * Clear table selection
     */
    clearSelection() {
        if (this.table) {
            this.table.clearSelection();
        }
    }
    
    /**
     * Update from state changes
     * @param {Object} applicationsState - Applications state
     */
    updateFromState(applicationsState) {
        // Update loading state
        if (applicationsState.isLoading !== undefined) {
            this.showLoading(applicationsState.isLoading);
        }
        
        // Update search query
        if (this.search && applicationsState.searchQuery !== undefined) {
            this.search.setQuery(applicationsState.searchQuery);
        }
    }
    
    /**
     * Destroy the table component
     */
    destroy() {
        if (this.table) {
            this.table.destroy();
        }
        
        if (this.pagination) {
            this.pagination.destroy();
        }
        
        if (this.search) {
            this.search.destroy();
        }
        
        console.log('Applications table destroyed');
    }
}
