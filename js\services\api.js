/**
 * API Service Module
 * 
 * Centralized API communication layer with enhanced error handling,
 * request/response interceptors, and automatic retry logic.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { events, EVENT_NAMES } from '../core/events.js';

class ApiService {
    constructor() {
        this.baseURL = 'http://127.0.0.1:8000/api';
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
        this.requestInterceptors = [];
        this.responseInterceptors = [];
        this.retryAttempts = 3;
        this.retryDelay = 1000; // 1 second
    }
    
    /**
     * Add request interceptor
     * @param {Function} interceptor - Function to modify request
     */
    addRequestInterceptor(interceptor) {
        this.requestInterceptors.push(interceptor);
    }
    
    /**
     * Add response interceptor
     * @param {Function} interceptor - Function to modify response
     */
    addResponseInterceptor(interceptor) {
        this.responseInterceptors.push(interceptor);
    }
    
    /**
     * Make HTTP request with enhanced error handling
     * @param {string} endpoint - API endpoint
     * @param {Object} options - Request options
     * @returns {Promise} Response promise
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        
        // Prepare request configuration
        let config = {
            headers: { ...this.defaultHeaders },
            ...options
        };
        
        // Apply request interceptors
        for (const interceptor of this.requestInterceptors) {
            config = await interceptor(config) || config;
        }
        
        // Emit loading event
        events.emit(EVENT_NAMES.DATA_LOADING, endpoint);
        
        let lastError;
        
        // Retry logic
        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                const response = await fetch(url, config);
                
                // Apply response interceptors
                let processedResponse = response;
                for (const interceptor of this.responseInterceptors) {
                    processedResponse = await interceptor(processedResponse) || processedResponse;
                }
                
                if (!processedResponse.ok) {
                    throw new Error(`HTTP ${processedResponse.status}: ${processedResponse.statusText}`);
                }
                
                const data = await processedResponse.json();
                
                // Emit success event
                events.emit(EVENT_NAMES.DATA_LOADED, endpoint, data);
                
                return data;
                
            } catch (error) {
                lastError = error;
                console.warn(`API request attempt ${attempt} failed:`, error.message);
                
                // Don't retry on client errors (4xx)
                if (error.message.includes('HTTP 4')) {
                    break;
                }
                
                // Wait before retry (except on last attempt)
                if (attempt < this.retryAttempts) {
                    await this.delay(this.retryDelay * attempt);
                }
            }
        }
        
        // All attempts failed
        console.error(`API request failed after ${this.retryAttempts} attempts:`, lastError);
        events.emit(EVENT_NAMES.DATA_ERROR, endpoint, lastError);
        throw lastError;
    }
    
    /**
     * GET request
     * @param {string} endpoint - API endpoint
     * @param {Object} params - Query parameters
     * @returns {Promise} Response promise
     */
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        
        return this.request(url, {
            method: 'GET'
        });
    }
    
    /**
     * POST request
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Request body data
     * @returns {Promise} Response promise
     */
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
    
    /**
     * PUT request
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Request body data
     * @returns {Promise} Response promise
     */
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }
    
    /**
     * DELETE request
     * @param {string} endpoint - API endpoint
     * @returns {Promise} Response promise
     */
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }
    
    /**
     * Utility delay function
     * @param {number} ms - Milliseconds to delay
     * @returns {Promise} Delay promise
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * Set base URL
     * @param {string} url - New base URL
     */
    setBaseURL(url) {
        this.baseURL = url;
    }
    
    /**
     * Set default headers
     * @param {Object} headers - Headers object
     */
    setDefaultHeaders(headers) {
        this.defaultHeaders = { ...this.defaultHeaders, ...headers };
    }
}

// Create singleton instance
const apiService = new ApiService();

// Export both the service and convenience functions
export { apiService };

// Chart Data API
export function getChartData() {
    return apiService.get('/chart-data');
}

// Applications API
export function getApplications(page = 1, limit = 10, search = '') {
    const params = { page: page.toString(), limit: limit.toString() };
    if (search) {
        params.search = search;
    }
    return apiService.get('/applications', params);
}

export function getApplication(id) {
    return apiService.get(`/applications/${id}`);
}

export function createApplication(applicationData) {
    return apiService.post('/applications', applicationData);
}

export function updateApplication(id, applicationData) {
    return apiService.put(`/applications/${id}`, applicationData);
}

export function deleteApplication(id) {
    return apiService.delete(`/applications/${id}`);
}
