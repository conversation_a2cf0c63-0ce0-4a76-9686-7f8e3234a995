/**
 * Dashboard Page Module
 * 
 * Handles dashboard-specific functionality including
 * charts, statistics, and dashboard widgets.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { events } from '../core/events.js';
import { state } from '../core/state.js';
import { loadChart } from '../ui/charts.js';

class DashboardController {
    constructor() {
        this.initialized = false;
        this.charts = new Map();
        this.widgets = new Map();
    }
    
    /**
     * Initialize the dashboard
     */
    async initialize() {
        if (this.initialized) {
            console.log('Dashboard already initialized');
            return;
        }
        
        try {
            console.log('Initializing dashboard...');
            
            // Check if dashboard elements exist
            if (!this.checkDashboardElements()) {
                console.warn('Dashboard elements not found');
                return;
            }
            
            // Initialize dashboard components
            await this.initializeComponents();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Load dashboard data
            await this.loadDashboardData();
            
            this.initialized = true;
            
            events.emit('dashboard:initialized');
            
            console.log('Dashboard initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize dashboard:', error);
            events.emit('app:error', error, 'dashboard_init');
        }
    }
    
    /**
     * Check if required dashboard elements exist
     * @returns {boolean} True if elements exist
     */
    checkDashboardElements() {
        const requiredElements = [
            'myChart' // Main chart element
        ];
        
        return requiredElements.some(id => {
            const element = document.getElementById(id);
            if (element) {
                return true;
            }
            return false;
        });
    }
    
    /**
     * Initialize dashboard components
     */
    async initializeComponents() {
        // Initialize main chart
        await this.initializeMainChart();
        
        // Initialize statistics widgets
        this.initializeStatistics();
        
        // Initialize other dashboard widgets
        this.initializeWidgets();
        
        console.log('Dashboard components initialized');
    }
    
    /**
     * Initialize main dashboard chart
     */
    async initializeMainChart() {
        try {
            const chart = await loadChart();
            if (chart) {
                this.charts.set('main', chart);
                console.log('Main chart initialized');
            }
        } catch (error) {
            console.error('Failed to initialize main chart:', error);
        }
    }
    
    /**
     * Initialize statistics widgets
     */
    initializeStatistics() {
        // Find statistics cards
        const statCards = document.querySelectorAll('[data-stat]');
        
        statCards.forEach(card => {
            const statType = card.getAttribute('data-stat');
            this.widgets.set(statType, {
                element: card,
                type: 'statistic',
                value: 0
            });
        });
        
        console.log(`Initialized ${statCards.length} statistics widgets`);
    }
    
    /**
     * Initialize other dashboard widgets
     */
    initializeWidgets() {
        // Find other dashboard widgets
        const widgets = document.querySelectorAll('[data-widget]');
        
        widgets.forEach(widget => {
            const widgetType = widget.getAttribute('data-widget');
            this.widgets.set(widgetType, {
                element: widget,
                type: 'widget'
            });
        });
        
        console.log(`Initialized ${widgets.length} dashboard widgets`);
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Listen for theme changes to update charts
        events.on('ui:theme:changed', (theme) => {
            this.handleThemeChange(theme);
        });
        
        // Listen for data updates
        events.on('dashboard:data:updated', (data) => {
            this.handleDataUpdate(data);
        });
        
        // Listen for chart events
        events.on('chart:loaded', (chartData) => {
            this.handleChartLoaded(chartData);
        });
        
        // Refresh button
        const refreshBtn = document.getElementById('dashboardRefresh');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refresh();
            });
        }
        
        console.log('Dashboard event listeners set up');
    }
    
    /**
     * Load dashboard data
     */
    async loadDashboardData() {
        try {
            // Set loading state
            this.setLoadingState(true);
            
            // Load statistics
            await this.loadStatistics();
            
            // Load recent activities
            await this.loadRecentActivities();
            
            // Load other dashboard data
            await this.loadWidgetData();
            
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            this.showError('Failed to load dashboard data');
        } finally {
            this.setLoadingState(false);
        }
    }
    
    /**
     * Load dashboard statistics
     */
    async loadStatistics() {
        try {
            // Mock statistics data - replace with actual API calls
            const stats = {
                totalApplications: 42,
                activeUsers: 128,
                totalDownloads: 1547,
                systemUptime: '99.9%'
            };
            
            // Update statistics widgets
            this.updateStatistic('totalApplications', stats.totalApplications);
            this.updateStatistic('activeUsers', stats.activeUsers);
            this.updateStatistic('totalDownloads', stats.totalDownloads);
            this.updateStatistic('systemUptime', stats.systemUptime);
            
            console.log('Statistics loaded');
            
        } catch (error) {
            console.error('Failed to load statistics:', error);
        }
    }
    
    /**
     * Load recent activities
     */
    async loadRecentActivities() {
        try {
            // Mock recent activities - replace with actual API calls
            const activities = [
                { id: 1, action: 'Application created', user: 'Admin', time: new Date() },
                { id: 2, action: 'User logged in', user: 'John Doe', time: new Date(Date.now() - 3600000) },
                { id: 3, action: 'Settings updated', user: 'Admin', time: new Date(Date.now() - 7200000) }
            ];
            
            this.updateRecentActivities(activities);
            
            console.log('Recent activities loaded');
            
        } catch (error) {
            console.error('Failed to load recent activities:', error);
        }
    }
    
    /**
     * Load widget data
     */
    async loadWidgetData() {
        // Load data for each widget
        for (const [widgetId, widget] of this.widgets) {
            if (widget.type === 'widget') {
                try {
                    await this.loadWidgetDataById(widgetId, widget);
                } catch (error) {
                    console.error(`Failed to load data for widget ${widgetId}:`, error);
                }
            }
        }
    }
    
    /**
     * Load data for specific widget
     * @param {string} widgetId - Widget ID
     * @param {Object} widget - Widget configuration
     */
    async loadWidgetDataById(widgetId, widget) {
        // Implement widget-specific data loading
        console.log(`Loading data for widget: ${widgetId}`);
    }
    
    /**
     * Update statistic value
     * @param {string} statType - Statistic type
     * @param {*} value - New value
     */
    updateStatistic(statType, value) {
        const widget = this.widgets.get(statType);
        if (widget && widget.element) {
            const valueElement = widget.element.querySelector('[data-value]');
            if (valueElement) {
                // Animate value change
                this.animateValue(valueElement, widget.value || 0, value);
                widget.value = value;
            }
        }
    }
    
    /**
     * Animate value change
     * @param {HTMLElement} element - Element to animate
     * @param {number} start - Start value
     * @param {number} end - End value
     */
    animateValue(element, start, end) {
        const duration = 1000; // 1 second
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            
            const current = start + (end - start) * easeOutQuart;
            
            if (typeof end === 'number') {
                element.textContent = Math.round(current).toLocaleString();
            } else {
                element.textContent = end; // For non-numeric values
            }
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    /**
     * Update recent activities
     * @param {Array} activities - Activities array
     */
    updateRecentActivities(activities) {
        const activitiesContainer = document.getElementById('recentActivities');
        if (!activitiesContainer) return;
        
        const activitiesList = activitiesContainer.querySelector('.list-group');
        if (!activitiesList) return;
        
        activitiesList.innerHTML = '';
        
        activities.forEach(activity => {
            const item = document.createElement('div');
            item.className = 'list-group-item list-group-item-action';
            item.innerHTML = `
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${activity.action}</h6>
                    <small class="text-muted">${this.formatRelativeTime(activity.time)}</small>
                </div>
                <p class="mb-1">by ${activity.user}</p>
            `;
            
            activitiesList.appendChild(item);
        });
    }
    
    /**
     * Format relative time
     * @param {Date} date - Date to format
     * @returns {string} Relative time string
     */
    formatRelativeTime(date) {
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        
        if (diffHours < 1) {
            return 'just now';
        } else if (diffHours < 24) {
            return `${diffHours}h ago`;
        } else {
            const diffDays = Math.floor(diffHours / 24);
            return `${diffDays}d ago`;
        }
    }
    
    /**
     * Handle theme change
     * @param {string} theme - New theme
     */
    handleThemeChange(theme) {
        // Charts will be updated automatically by the chart manager
        console.log(`Dashboard theme changed to: ${theme}`);
    }
    
    /**
     * Handle data update
     * @param {Object} data - Updated data
     */
    handleDataUpdate(data) {
        console.log('Dashboard data updated:', data);
        // Refresh relevant components
    }
    
    /**
     * Handle chart loaded
     * @param {Object} chartData - Chart data
     */
    handleChartLoaded(chartData) {
        console.log('Chart loaded on dashboard:', chartData.id);
    }
    
    /**
     * Set loading state
     * @param {boolean} loading - Loading state
     */
    setLoadingState(loading) {
        const loadingElements = document.querySelectorAll('.dashboard-loading');
        loadingElements.forEach(element => {
            element.style.display = loading ? 'block' : 'none';
        });
        
        const contentElements = document.querySelectorAll('.dashboard-content');
        contentElements.forEach(element => {
            element.style.opacity = loading ? '0.5' : '1';
        });
    }
    
    /**
     * Show error message
     * @param {string} message - Error message
     */
    showError(message) {
        console.error('Dashboard error:', message);
        // Could show a toast notification here
    }
    
    /**
     * Refresh dashboard
     */
    async refresh() {
        console.log('Refreshing dashboard...');
        await this.loadDashboardData();
        
        // Refresh charts
        this.charts.forEach(chart => {
            if (chart && chart.update) {
                chart.update();
            }
        });
    }
    
    /**
     * Cleanup dashboard
     */
    cleanup() {
        // Destroy charts
        this.charts.forEach(chart => {
            if (chart && chart.destroy) {
                chart.destroy();
            }
        });
        
        this.charts.clear();
        this.widgets.clear();
        this.initialized = false;
        
        console.log('Dashboard cleaned up');
    }
}

// Create singleton instance
const dashboardController = new DashboardController();

// Export initialization function
export function initializeDashboard() {
    dashboardController.initialize();
}

// Export controller for advanced usage
export { dashboardController };
