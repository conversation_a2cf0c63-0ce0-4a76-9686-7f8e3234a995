/**
 * Applications Page Controller
 * 
 * Main controller for the applications page that coordinates
 * form, table, and other components.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { events, EVENT_NAMES } from '../../core/events.js';
import { state } from '../../core/state.js';
import { ApplicationForm } from './form.js';
import { ApplicationTable } from './table.js';

class ApplicationsPageController {
    constructor() {
        this.form = null;
        this.table = null;
        this.initialized = false;
    }
    
    /**
     * Initialize the applications page
     */
    async initialize() {
        if (this.initialized) {
            console.log('Applications page already initialized');
            return;
        }
        
        try {
            console.log('Initializing applications page...');
            
            // Check if page elements exist
            if (!this.checkPageElements()) {
                console.warn('Applications page elements not found');
                return;
            }
            
            // Initialize components
            await this.initializeComponents();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Load initial data
            await this.loadInitialData();
            
            this.initialized = true;
            
            // Emit page initialized event
            events.emit('applications:page:initialized');
            
            console.log('Applications page initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize applications page:', error);
            events.emit(EVENT_NAMES.APP_ERROR, error, 'applications_page_init');
        }
    }
    
    /**
     * Check if required page elements exist
     * @returns {boolean} True if elements exist
     */
    checkPageElements() {
        const requiredElements = [
            'applicationForm',
            'tableBody',
            'paginationControls',
            'searchInput'
        ];
        
        return requiredElements.every(id => {
            const element = document.getElementById(id);
            if (!element) {
                console.warn(`Required element not found: ${id}`);
                return false;
            }
            return true;
        });
    }
    
    /**
     * Initialize page components
     */
    async initializeComponents() {
        // Initialize form component
        this.form = new ApplicationForm('applicationForm', {
            onSubmit: (data) => this.handleFormSubmit(data),
            onReset: () => this.handleFormReset()
        });
        
        // Initialize table component
        this.table = new ApplicationTable('applicationsTableContainer', {
            onEdit: (id) => this.handleEditApplication(id),
            onDelete: (id) => this.handleDeleteApplication(id),
            onSelectionChange: (selection) => this.handleSelectionChange(selection)
        });
        
        console.log('Applications page components initialized');
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Listen for global events
        events.on('applications:created', (data) => {
            this.handleApplicationCreated(data);
        });
        
        events.on('applications:updated', (data) => {
            this.handleApplicationUpdated(data);
        });
        
        events.on('applications:deleted', (id) => {
            this.handleApplicationDeleted(id);
        });
        
        // Listen for search events
        events.on('search:performed', (searchData) => {
            this.handleSearch(searchData);
        });
        
        // Listen for pagination events
        events.on('pagination:changed', (paginationData) => {
            this.handlePaginationChange(paginationData);
        });
        
        // Listen for state changes
        state.subscribe('applications', (applicationsState) => {
            this.handleStateChange(applicationsState);
        });
        
        console.log('Applications page event listeners set up');
    }
    
    /**
     * Load initial data
     */
    async loadInitialData() {
        try {
            // Set loading state
            state.set('applications.isLoading', true);
            
            // Load applications data
            await this.table.loadData();
            
        } catch (error) {
            console.error('Failed to load initial data:', error);
            events.emit(EVENT_NAMES.APP_ERROR, error, 'applications_data_load');
        } finally {
            state.set('applications.isLoading', false);
        }
    }
    
    /**
     * Handle form submission
     * @param {Object} formData - Form data
     */
    async handleFormSubmit(formData) {
        try {
            const editingId = state.get('applications.editingApplicationId');
            
            if (editingId) {
                // Update existing application
                await this.form.updateApplication(editingId, formData);
                events.emit('applications:updated', { id: editingId, data: formData });
            } else {
                // Create new application
                const result = await this.form.createApplication(formData);
                events.emit('applications:created', result);
            }
            
        } catch (error) {
            console.error('Form submission error:', error);
            events.emit(EVENT_NAMES.APP_ERROR, error, 'form_submission');
        }
    }
    
    /**
     * Handle form reset
     */
    handleFormReset() {
        state.update({
            'applications.editingApplicationId': null,
            'applications.selectedRowId': null
        });
        
        // Clear table selection
        if (this.table) {
            this.table.clearSelection();
        }
    }
    
    /**
     * Handle edit application request
     * @param {number} id - Application ID
     */
    async handleEditApplication(id) {
        try {
            // Set editing state
            state.set('applications.editingApplicationId', id);
            
            // Load application data into form
            await this.form.loadApplicationForEdit(id);
            
            events.emit('applications:edit:started', id);
            
        } catch (error) {
            console.error('Edit application error:', error);
            events.emit(EVENT_NAMES.APP_ERROR, error, 'edit_application');
        }
    }
    
    /**
     * Handle delete application request
     * @param {number} id - Application ID
     */
    async handleDeleteApplication(id) {
        try {
            // Show confirmation dialog
            const confirmed = await this.showDeleteConfirmation();
            
            if (confirmed) {
                await this.table.deleteApplication(id);
                events.emit('applications:deleted', id);
            }
            
        } catch (error) {
            console.error('Delete application error:', error);
            events.emit(EVENT_NAMES.APP_ERROR, error, 'delete_application');
        }
    }
    
    /**
     * Handle table selection change
     * @param {Object} selection - Selection data
     */
    handleSelectionChange(selection) {
        state.set('applications.selectedRowId', selection.selectedIds[0] || null);
    }
    
    /**
     * Handle application created
     * @param {Object} data - Created application data
     */
    handleApplicationCreated(data) {
        // Refresh table data
        this.table.loadData();
        
        // Reset form
        this.form.reset();
        
        // Show success message
        this.showSuccessMessage('Application created successfully!');
    }
    
    /**
     * Handle application updated
     * @param {Object} data - Updated application data
     */
    handleApplicationUpdated(data) {
        // Refresh table data
        this.table.loadData();
        
        // Reset form
        this.form.reset();
        
        // Show success message
        this.showSuccessMessage('Application updated successfully!');
    }
    
    /**
     * Handle application deleted
     * @param {number} id - Deleted application ID
     */
    handleApplicationDeleted(id) {
        // Refresh table data
        this.table.loadData();
        
        // Clear selection if deleted item was selected
        const selectedId = state.get('applications.selectedRowId');
        if (selectedId == id) {
            state.set('applications.selectedRowId', null);
        }
        
        // Show success message
        this.showSuccessMessage('Application deleted successfully!');
    }
    
    /**
     * Handle search
     * @param {Object} searchData - Search data
     */
    handleSearch(searchData) {
        // Update state
        state.update({
            'applications.searchQuery': searchData.query,
            'applications.currentPage': 1
        });
        
        // Reload table data
        this.table.loadData();
    }
    
    /**
     * Handle pagination change
     * @param {Object} paginationData - Pagination data
     */
    handlePaginationChange(paginationData) {
        // Update state
        state.update({
            'applications.currentPage': paginationData.currentPage,
            'applications.itemsPerPage': paginationData.itemsPerPage
        });
        
        // Reload table data
        this.table.loadData();
    }
    
    /**
     * Handle state changes
     * @param {Object} applicationsState - Applications state
     */
    handleStateChange(applicationsState) {
        // Update components based on state changes
        if (this.table) {
            this.table.updateFromState(applicationsState);
        }
        
        if (this.form) {
            this.form.updateFromState(applicationsState);
        }
    }
    
    /**
     * Show delete confirmation dialog
     * @returns {Promise<boolean>} User confirmation
     */
    async showDeleteConfirmation() {
        // Import modal component dynamically
        const { Modal } = await import('../../components/modal.js');
        
        return Modal.confirm({
            title: 'Delete Application',
            message: 'Are you sure you want to delete this application? This action cannot be undone.',
            confirmText: 'Delete',
            confirmClass: 'btn-danger'
        });
    }
    
    /**
     * Show success message
     * @param {string} message - Success message
     */
    showSuccessMessage(message) {
        // For now, use alert - can be replaced with toast notifications
        alert(message);
    }
    
    /**
     * Cleanup when page is unloaded
     */
    cleanup() {
        if (this.form) {
            this.form.destroy();
        }
        
        if (this.table) {
            this.table.destroy();
        }
        
        this.initialized = false;
        
        console.log('Applications page cleaned up');
    }
}

// Create singleton instance
const applicationsPageController = new ApplicationsPageController();

// Export initialization function for compatibility
export function initializeApplicationsPage() {
    applicationsPageController.initialize();
}

// Export controller for advanced usage
export { applicationsPageController };
