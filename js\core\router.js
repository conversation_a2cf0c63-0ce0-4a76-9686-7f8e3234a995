/**
 * Router Module
 * 
 * Handles navigation and page loading with enhanced features
 * including loading states, error handling, and page lifecycle events.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { events, EVENT_NAMES } from './events.js';
import { state } from './state.js';

class Router {
    constructor() {
        this.routes = new Map();
        this.currentPage = null;
        this.isLoading = false;
        
        // Register default routes
        this.registerDefaultRoutes();
    }
    
    /**
     * Register a route with its configuration
     * @param {string} path - Route path (e.g., 'content/dashboard.html')
     * @param {Object} config - Route configuration
     */
    registerRoute(path, config = {}) {
        this.routes.set(path, {
            path,
            title: config.title || 'Page',
            onLoad: config.onLoad || null,
            onUnload: config.onUnload || null,
            requiresAuth: config.requiresAuth || false,
            ...config
        });
    }
    
    /**
     * Register default application routes
     */
    registerDefaultRoutes() {
        this.registerRoute('content/dashboard.html', {
            title: 'Dashboard',
            onLoad: () => {
                // Load dashboard-specific functionality
                import('../pages/dashboard.js').then(module => {
                    if (module.initializeDashboard) {
                        module.initializeDashboard();
                    }
                });
            }
        });
        
        this.registerRoute('content/applications.html', {
            title: 'Applications',
            onLoad: () => {
                // Load applications page
                import('../pages/applications/index.js').then(module => {
                    if (module.initializeApplicationsPage) {
                        module.initializeApplicationsPage();
                    }
                });
            }
        });
        
        this.registerRoute('content/user-management.html', {
            title: 'User Management',
            onLoad: () => {
                // Load user management functionality
                import('../pages/user-management.js').then(module => {
                    if (module.initializeUserManagement) {
                        module.initializeUserManagement();
                    }
                });
            }
        });
    }
    
    /**
     * Navigate to a page
     * @param {string} path - Page path
     * @param {HTMLElement} clickedLink - The clicked navigation link (optional)
     * @returns {Promise} Promise that resolves when navigation is complete
     */
    async navigateTo(path, clickedLink = null) {
        if (this.isLoading) {
            console.warn('Navigation already in progress');
            return;
        }
        
        try {
            this.isLoading = true;
            events.emit(EVENT_NAMES.PAGE_LOADING, path);
            
            // Get route configuration
            const route = this.routes.get(path);
            
            // Call onUnload for current page
            if (this.currentPage && this.currentPage.onUnload) {
                await this.currentPage.onUnload();
            }
            
            // Fetch and load the page content
            const response = await fetch(path);
            if (!response.ok) {
                throw new Error(`Failed to load page: ${response.status} ${response.statusText}`);
            }
            
            const content = await response.text();
            
            // Update main content
            const mainContent = document.getElementById('main-content');
            if (mainContent) {
                mainContent.innerHTML = content;
            }
            
            // Update navigation state
            this.updateNavigationState(clickedLink, path);
            
            // Update application state
            state.set('currentPage', this.getPageNameFromPath(path));
            
            // Set current page
            this.currentPage = route;
            
            // Call onLoad for new page
            if (route && route.onLoad) {
                await route.onLoad();
            }
            
            // Emit page loaded event
            events.emit(EVENT_NAMES.PAGE_LOADED, path, route);
            events.emit(EVENT_NAMES.PAGE_CHANGED, path, route);
            
            return { success: true, path, route };
            
        } catch (error) {
            console.error('Navigation error:', error);
            events.emit(EVENT_NAMES.APP_ERROR, error, 'navigation');
            
            // Show error page or fallback
            this.showErrorPage(error);
            
            throw error;
        } finally {
            this.isLoading = false;
        }
    }
    
    /**
     * Update navigation link states
     * @param {HTMLElement} clickedLink - The clicked link
     * @param {string} path - Current page path
     */
    updateNavigationState(clickedLink, path) {
        // Deactivate all navigation links
        document.querySelectorAll('#left-navbar-container .nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        // Activate the appropriate link
        if (clickedLink) {
            clickedLink.classList.add('active');
        } else {
            // Find and activate the link for this path
            const targetLink = document.querySelector(`#left-navbar-container .nav-link[data-page="${path}"]`);
            if (targetLink) {
                targetLink.classList.add('active');
            }
        }
    }
    
    /**
     * Get page name from path
     * @param {string} path - Page path
     * @returns {string} Page name
     */
    getPageNameFromPath(path) {
        const filename = path.split('/').pop();
        return filename.replace('.html', '');
    }
    
    /**
     * Show error page
     * @param {Error} error - The error that occurred
     */
    showErrorPage(error) {
        const mainContent = document.getElementById('main-content');
        if (mainContent) {
            mainContent.innerHTML = `
                <div class="container-fluid">
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="mb-0">
                                        <i class="bi bi-exclamation-triangle me-2"></i>
                                        Page Load Error
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted">Sorry, we couldn't load the requested page.</p>
                                    <p><strong>Error:</strong> ${error.message}</p>
                                    <button class="btn btn-primary" onclick="location.reload()">
                                        <i class="bi bi-arrow-clockwise me-1"></i>
                                        Reload Page
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }
    
    /**
     * Initialize router and set up navigation event listeners
     */
    initialize() {
        // Set up navigation link event listeners
        document.addEventListener('click', (e) => {
            const link = e.target.closest('[data-page]');
            if (link) {
                e.preventDefault();
                const page = link.getAttribute('data-page');
                this.navigateTo(page, link);
            }
        });
        
        console.log('Router initialized');
    }
    
    /**
     * Get current route information
     * @returns {Object|null} Current route configuration
     */
    getCurrentRoute() {
        return this.currentPage;
    }
    
    /**
     * Check if currently loading
     * @returns {boolean} Loading state
     */
    isNavigating() {
        return this.isLoading;
    }
}

// Create and export singleton instance
export const router = new Router();
