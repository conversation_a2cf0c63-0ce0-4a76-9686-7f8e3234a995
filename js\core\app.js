/**
 * Main Application Controller
 * 
 * Orchestrates the initialization and coordination of all application modules.
 * Handles the application lifecycle and provides the main entry point.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { events, EVENT_NAMES } from './events.js';
import { state } from './state.js';
import { router } from './router.js';

class Application {
    constructor() {
        this.initialized = false;
        this.modules = new Map();
        this.startTime = Date.now();
    }
    
    /**
     * Initialize the application
     */
    async initialize() {
        if (this.initialized) {
            console.warn('Application already initialized');
            return;
        }
        
        try {
            console.log('Initializing application...');
            
            // Initialize core modules
            await this.initializeCore();
            
            // Load and initialize UI modules
            await this.initializeUI();
            
            // Load initial page
            await this.loadInitialPage();
            
            // Set up global error handling
            this.setupErrorHandling();
            
            // Mark as initialized
            this.initialized = true;
            
            // Emit initialization complete event
            events.emit(EVENT_NAMES.APP_INITIALIZED, {
                startTime: this.startTime,
                initTime: Date.now() - this.startTime
            });
            
            console.log(`Application initialized in ${Date.now() - this.startTime}ms`);
            
        } catch (error) {
            console.error('Failed to initialize application:', error);
            events.emit(EVENT_NAMES.APP_ERROR, error, 'initialization');
            throw error;
        }
    }
    
    /**
     * Initialize core modules
     */
    async initializeCore() {
        // Initialize router
        router.initialize();
        
        // Set up state subscriptions for debugging in development
        if (this.isDevelopment()) {
            this.setupStateDebugging();
        }
        
        console.log('Core modules initialized');
    }
    
    /**
     * Initialize UI modules
     */
    async initializeUI() {
        // Load navigation bars
        await this.loadNavigationBars();
        
        // Initialize theme system
        const { initializeTheme } = await import('../ui/theme.js');
        initializeTheme();
        
        console.log('UI modules initialized');
    }
    
    /**
     * Load navigation bars
     */
    async loadNavigationBars() {
        try {
            // Load top navbar
            const topNavResponse = await fetch('top-navbar.html');
            const topNavContent = await topNavResponse.text();
            const topNavContainer = document.getElementById('top-navbar-container');
            if (topNavContainer) {
                topNavContainer.innerHTML = topNavContent;
            }
            
            // Load left navbar
            const leftNavResponse = await fetch('left-navbar.html');
            const leftNavContent = await leftNavResponse.text();
            const leftNavContainer = document.getElementById('left-navbar-container');
            if (leftNavContainer) {
                leftNavContainer.innerHTML = leftNavContent;
            }
            
            console.log('Navigation bars loaded');
            
        } catch (error) {
            console.error('Failed to load navigation bars:', error);
            throw error;
        }
    }
    
    /**
     * Load the initial page
     */
    async loadInitialPage() {
        const initialPage = 'content/dashboard.html';
        await router.navigateTo(initialPage);
    }
    
    /**
     * Set up global error handling
     */
    setupErrorHandling() {
        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            events.emit(EVENT_NAMES.APP_ERROR, event.reason, 'unhandled_promise');
        });
        
        // Handle general errors
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            events.emit(EVENT_NAMES.APP_ERROR, event.error, 'global');
        });
        
        // Subscribe to application errors
        events.on(EVENT_NAMES.APP_ERROR, (error, context) => {
            this.handleError(error, context);
        });
    }
    
    /**
     * Handle application errors
     * @param {Error} error - The error that occurred
     * @param {string} context - Context where the error occurred
     */
    handleError(error, context) {
        console.error(`Application error in ${context}:`, error);
        
        // In production, you might want to send errors to a logging service
        if (!this.isDevelopment()) {
            // Send to error tracking service
            // this.sendErrorToService(error, context);
        }
        
        // Show user-friendly error message for critical errors
        if (context === 'initialization' || context === 'navigation') {
            this.showCriticalError(error, context);
        }
    }
    
    /**
     * Show critical error to user
     * @param {Error} error - The error
     * @param {string} context - Error context
     */
    showCriticalError(error, context) {
        // You could implement a modal or notification system here
        console.error(`Critical error in ${context}:`, error.message);
    }
    
    /**
     * Set up state debugging for development
     */
    setupStateDebugging() {
        // Log state changes in development
        const originalSet = state.set.bind(state);
        state.set = (path, value) => {
            console.log(`State change: ${path} =`, value);
            return originalSet(path, value);
        };
    }
    
    /**
     * Check if running in development mode
     * @returns {boolean} True if in development
     */
    isDevelopment() {
        return location.hostname === 'localhost' || location.hostname === '127.0.0.1';
    }
    
    /**
     * Register a module
     * @param {string} name - Module name
     * @param {Object} module - Module instance
     */
    registerModule(name, module) {
        this.modules.set(name, module);
        console.log(`Module registered: ${name}`);
    }
    
    /**
     * Get a registered module
     * @param {string} name - Module name
     * @returns {Object|null} Module instance
     */
    getModule(name) {
        return this.modules.get(name) || null;
    }
    
    /**
     * Get application information
     * @returns {Object} Application info
     */
    getInfo() {
        return {
            initialized: this.initialized,
            startTime: this.startTime,
            uptime: Date.now() - this.startTime,
            modules: Array.from(this.modules.keys()),
            currentPage: state.get('currentPage'),
            isDevelopment: this.isDevelopment()
        };
    }
}

// Create and export singleton instance
export const app = new Application();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => app.initialize());
} else {
    // DOM is already ready
    app.initialize();
}
