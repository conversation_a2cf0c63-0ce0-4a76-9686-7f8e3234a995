/**
 * Validation Service Module
 * 
 * Provides comprehensive form validation utilities with custom rules,
 * real-time validation, and Bootstrap integration.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

class ValidationService {
    constructor() {
        this.rules = new Map();
        this.messages = new Map();
        this.setupDefaultRules();
        this.setupDefaultMessages();
    }
    
    /**
     * Set up default validation rules
     */
    setupDefaultRules() {
        this.addRule('required', (value) => {
            return value !== null && value !== undefined && value.toString().trim() !== '';
        });
        
        this.addRule('email', (value) => {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return !value || emailRegex.test(value);
        });
        
        this.addRule('url', (value) => {
            try {
                new URL(value);
                return true;
            } catch {
                return !value; // Allow empty values
            }
        });
        
        this.addRule('minLength', (value, min) => {
            return !value || value.toString().length >= parseInt(min);
        });
        
        this.addRule('maxLength', (value, max) => {
            return !value || value.toString().length <= parseInt(max);
        });
        
        this.addRule('min', (value, min) => {
            return !value || parseFloat(value) >= parseFloat(min);
        });
        
        this.addRule('max', (value, max) => {
            return !value || parseFloat(value) <= parseFloat(max);
        });
        
        this.addRule('pattern', (value, pattern) => {
            if (!value) return true;
            const regex = new RegExp(pattern);
            return regex.test(value);
        });
        
        this.addRule('date', (value) => {
            if (!value) return true;
            const date = new Date(value);
            return !isNaN(date.getTime());
        });
        
        this.addRule('futureDate', (value) => {
            if (!value) return true;
            const date = new Date(value);
            return date > new Date();
        });
        
        this.addRule('pastDate', (value) => {
            if (!value) return true;
            const date = new Date(value);
            return date < new Date();
        });
    }
    
    /**
     * Set up default error messages
     */
    setupDefaultMessages() {
        this.setMessage('required', 'This field is required.');
        this.setMessage('email', 'Please enter a valid email address.');
        this.setMessage('url', 'Please enter a valid URL.');
        this.setMessage('minLength', 'Must be at least {0} characters long.');
        this.setMessage('maxLength', 'Must be no more than {0} characters long.');
        this.setMessage('min', 'Must be at least {0}.');
        this.setMessage('max', 'Must be no more than {0}.');
        this.setMessage('pattern', 'Please match the required format.');
        this.setMessage('date', 'Please enter a valid date.');
        this.setMessage('futureDate', 'Date must be in the future.');
        this.setMessage('pastDate', 'Date must be in the past.');
    }
    
    /**
     * Add a custom validation rule
     * @param {string} name - Rule name
     * @param {Function} validator - Validation function
     */
    addRule(name, validator) {
        this.rules.set(name, validator);
    }
    
    /**
     * Set error message for a rule
     * @param {string} rule - Rule name
     * @param {string} message - Error message
     */
    setMessage(rule, message) {
        this.messages.set(rule, message);
    }
    
    /**
     * Validate a single value against rules
     * @param {*} value - Value to validate
     * @param {Array|string} rules - Validation rules
     * @returns {Object} Validation result
     */
    validateValue(value, rules) {
        if (typeof rules === 'string') {
            rules = this.parseRules(rules);
        }
        
        const errors = [];
        
        for (const rule of rules) {
            const { name, params } = rule;
            const validator = this.rules.get(name);
            
            if (!validator) {
                console.warn(`Unknown validation rule: ${name}`);
                continue;
            }
            
            const isValid = validator(value, ...params);
            
            if (!isValid) {
                const message = this.formatMessage(name, params);
                errors.push({ rule: name, message });
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    
    /**
     * Validate a form element
     * @param {HTMLElement} element - Form element to validate
     * @returns {Object} Validation result
     */
    validateElement(element) {
        const value = this.getElementValue(element);
        const rules = this.getElementRules(element);
        
        const result = this.validateValue(value, rules);
        
        // Update element UI
        this.updateElementUI(element, result);
        
        return result;
    }
    
    /**
     * Validate entire form
     * @param {HTMLFormElement} form - Form to validate
     * @returns {Object} Validation result
     */
    validateForm(form) {
        const elements = form.querySelectorAll('[data-validate], [required]');
        const results = new Map();
        let isValid = true;
        
        elements.forEach(element => {
            const result = this.validateElement(element);
            results.set(element.name || element.id, result);
            
            if (!result.isValid) {
                isValid = false;
            }
        });
        
        return {
            isValid,
            results: Object.fromEntries(results)
        };
    }
    
    /**
     * Set up real-time validation for a form
     * @param {HTMLFormElement} form - Form element
     */
    setupRealTimeValidation(form) {
        const elements = form.querySelectorAll('[data-validate], [required]');
        
        elements.forEach(element => {
            // Validate on blur
            element.addEventListener('blur', () => {
                this.validateElement(element);
            });
            
            // Clear errors on input (for better UX)
            element.addEventListener('input', () => {
                if (element.classList.contains('is-invalid')) {
                    this.clearElementErrors(element);
                }
            });
        });
    }
    
    /**
     * Parse validation rules from string
     * @param {string} rulesString - Rules string (e.g., "required|minLength:3|email")
     * @returns {Array} Parsed rules array
     */
    parseRules(rulesString) {
        return rulesString.split('|').map(rule => {
            const [name, ...paramParts] = rule.split(':');
            const params = paramParts.length > 0 ? paramParts.join(':').split(',') : [];
            return { name: name.trim(), params };
        });
    }
    
    /**
     * Get element value based on type
     * @param {HTMLElement} element - Form element
     * @returns {*} Element value
     */
    getElementValue(element) {
        if (element.type === 'checkbox') {
            return element.checked;
        } else if (element.type === 'radio') {
            const form = element.closest('form');
            const checked = form.querySelector(`input[name="${element.name}"]:checked`);
            return checked ? checked.value : '';
        } else {
            return element.value;
        }
    }
    
    /**
     * Get validation rules for element
     * @param {HTMLElement} element - Form element
     * @returns {Array} Rules array
     */
    getElementRules(element) {
        const rules = [];
        
        // Check for required attribute
        if (element.hasAttribute('required')) {
            rules.push({ name: 'required', params: [] });
        }
        
        // Check for data-validate attribute
        const validateAttr = element.getAttribute('data-validate');
        if (validateAttr) {
            rules.push(...this.parseRules(validateAttr));
        }
        
        // Check for HTML5 validation attributes
        if (element.type === 'email') {
            rules.push({ name: 'email', params: [] });
        }
        
        if (element.type === 'url') {
            rules.push({ name: 'url', params: [] });
        }
        
        if (element.minLength) {
            rules.push({ name: 'minLength', params: [element.minLength] });
        }
        
        if (element.maxLength) {
            rules.push({ name: 'maxLength', params: [element.maxLength] });
        }
        
        if (element.min) {
            rules.push({ name: 'min', params: [element.min] });
        }
        
        if (element.max) {
            rules.push({ name: 'max', params: [element.max] });
        }
        
        if (element.pattern) {
            rules.push({ name: 'pattern', params: [element.pattern] });
        }
        
        return rules;
    }
    
    /**
     * Update element UI based on validation result
     * @param {HTMLElement} element - Form element
     * @param {Object} result - Validation result
     */
    updateElementUI(element, result) {
        // Remove existing classes
        element.classList.remove('is-valid', 'is-invalid');
        
        // Clear existing feedback
        this.clearElementErrors(element);
        
        if (result.isValid) {
            element.classList.add('is-valid');
        } else {
            element.classList.add('is-invalid');
            this.showElementErrors(element, result.errors);
        }
    }
    
    /**
     * Show validation errors for element
     * @param {HTMLElement} element - Form element
     * @param {Array} errors - Error array
     */
    showElementErrors(element, errors) {
        const feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        feedback.textContent = errors[0].message; // Show first error
        
        element.parentNode.appendChild(feedback);
    }
    
    /**
     * Clear validation errors for element
     * @param {HTMLElement} element - Form element
     */
    clearElementErrors(element) {
        const feedback = element.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.remove();
        }
    }
    
    /**
     * Format error message with parameters
     * @param {string} rule - Rule name
     * @param {Array} params - Rule parameters
     * @returns {string} Formatted message
     */
    formatMessage(rule, params) {
        let message = this.messages.get(rule) || 'Validation failed.';
        
        // Replace placeholders with parameters
        params.forEach((param, index) => {
            message = message.replace(`{${index}}`, param);
        });
        
        return message;
    }
}

// Create and export singleton instance
export const validation = new ValidationService();
