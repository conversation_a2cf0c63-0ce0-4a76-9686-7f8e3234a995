/**
 * Modal Component
 * 
 * Reusable modal component with Bootstrap integration
 * and customizable content and actions.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { events } from '../core/events.js';

export class Modal {
    constructor(options = {}) {
        this.options = {
            id: 'dynamic-modal',
            title: 'Modal',
            size: '', // 'sm', 'lg', 'xl'
            backdrop: true,
            keyboard: true,
            focus: true,
            ...options
        };
        
        this.element = null;
        this.bootstrapModal = null;
        this.isVisible = false;
        
        this.init();
    }
    
    /**
     * Initialize the modal
     */
    init() {
        this.createElement();
        this.setupEventListeners();
    }
    
    /**
     * Create modal element
     */
    createElement() {
        // Remove existing modal with same ID
        const existing = document.getElementById(this.options.id);
        if (existing) {
            existing.remove();
        }
        
        this.element = document.createElement('div');
        this.element.className = 'modal fade';
        this.element.id = this.options.id;
        this.element.tabIndex = -1;
        this.element.setAttribute('aria-hidden', 'true');
        
        const dialog = document.createElement('div');
        dialog.className = `modal-dialog${this.options.size ? ` modal-${this.options.size}` : ''}`;
        
        const content = document.createElement('div');
        content.className = 'modal-content';
        
        // Header
        const header = document.createElement('div');
        header.className = 'modal-header';
        
        const title = document.createElement('h5');
        title.className = 'modal-title';
        title.textContent = this.options.title;
        
        const closeBtn = document.createElement('button');
        closeBtn.type = 'button';
        closeBtn.className = 'btn-close';
        closeBtn.setAttribute('data-bs-dismiss', 'modal');
        closeBtn.setAttribute('aria-label', 'Close');
        
        header.appendChild(title);
        header.appendChild(closeBtn);
        
        // Body
        const body = document.createElement('div');
        body.className = 'modal-body';
        
        // Footer
        const footer = document.createElement('div');
        footer.className = 'modal-footer';
        
        content.appendChild(header);
        content.appendChild(body);
        content.appendChild(footer);
        dialog.appendChild(content);
        this.element.appendChild(dialog);
        
        document.body.appendChild(this.element);
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        this.element.addEventListener('shown.bs.modal', () => {
            this.isVisible = true;
            events.emit('modal:shown', this.options.id);
        });
        
        this.element.addEventListener('hidden.bs.modal', () => {
            this.isVisible = false;
            events.emit('modal:hidden', this.options.id);
        });
        
        // Handle action button clicks
        this.element.addEventListener('click', (e) => {
            const actionBtn = e.target.closest('[data-action]');
            if (actionBtn) {
                const action = actionBtn.getAttribute('data-action');
                events.emit('modal:action', { action, modal: this });
            }
        });
    }
    
    /**
     * Set modal title
     * @param {string} title - Modal title
     */
    setTitle(title) {
        const titleElement = this.element.querySelector('.modal-title');
        if (titleElement) {
            titleElement.textContent = title;
        }
    }
    
    /**
     * Set modal body content
     * @param {string|HTMLElement} content - Body content
     */
    setBody(content) {
        const body = this.element.querySelector('.modal-body');
        if (body) {
            if (typeof content === 'string') {
                body.innerHTML = content;
            } else {
                body.innerHTML = '';
                body.appendChild(content);
            }
        }
    }
    
    /**
     * Set modal footer content
     * @param {string|HTMLElement|Array} content - Footer content
     */
    setFooter(content) {
        const footer = this.element.querySelector('.modal-footer');
        if (footer) {
            footer.innerHTML = '';
            
            if (Array.isArray(content)) {
                // Array of button configurations
                content.forEach(btn => {
                    footer.appendChild(this.createButton(btn));
                });
            } else if (typeof content === 'string') {
                footer.innerHTML = content;
            } else {
                footer.appendChild(content);
            }
        }
    }
    
    /**
     * Create button element
     * @param {Object} config - Button configuration
     * @returns {HTMLElement} Button element
     */
    createButton(config) {
        const button = document.createElement('button');
        button.type = 'button';
        button.className = `btn ${config.className || 'btn-secondary'}`;
        button.textContent = config.text || 'Button';
        
        if (config.action) {
            button.setAttribute('data-action', config.action);
        }
        
        if (config.dismiss) {
            button.setAttribute('data-bs-dismiss', 'modal');
        }
        
        if (config.disabled) {
            button.disabled = true;
        }
        
        return button;
    }
    
    /**
     * Show the modal
     */
    show() {
        if (!this.bootstrapModal) {
            // Initialize Bootstrap modal
            this.bootstrapModal = new bootstrap.Modal(this.element, {
                backdrop: this.options.backdrop,
                keyboard: this.options.keyboard,
                focus: this.options.focus
            });
        }
        
        this.bootstrapModal.show();
    }
    
    /**
     * Hide the modal
     */
    hide() {
        if (this.bootstrapModal) {
            this.bootstrapModal.hide();
        }
    }
    
    /**
     * Toggle modal visibility
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }
    
    /**
     * Destroy the modal
     */
    destroy() {
        if (this.bootstrapModal) {
            this.bootstrapModal.dispose();
        }
        
        if (this.element) {
            this.element.remove();
        }
        
        this.element = null;
        this.bootstrapModal = null;
        this.isVisible = false;
    }
    
    /**
     * Create and show a confirmation modal
     * @param {Object} options - Modal options
     * @returns {Promise} Promise that resolves with user choice
     */
    static confirm(options = {}) {
        return new Promise((resolve) => {
            const modal = new Modal({
                id: 'confirm-modal',
                title: options.title || 'Confirm',
                size: options.size || 'sm'
            });
            
            modal.setBody(options.message || 'Are you sure?');
            modal.setFooter([
                {
                    text: options.cancelText || 'Cancel',
                    className: 'btn-secondary',
                    action: 'cancel',
                    dismiss: true
                },
                {
                    text: options.confirmText || 'Confirm',
                    className: options.confirmClass || 'btn-primary',
                    action: 'confirm'
                }
            ]);
            
            // Handle actions
            const handleAction = (e) => {
                if (e.detail.action === 'confirm') {
                    resolve(true);
                    modal.hide();
                } else if (e.detail.action === 'cancel') {
                    resolve(false);
                }
            };
            
            events.on('modal:action', handleAction);
            
            // Clean up when modal is hidden
            modal.element.addEventListener('hidden.bs.modal', () => {
                events.off('modal:action', handleAction);
                modal.destroy();
            });
            
            modal.show();
        });
    }
    
    /**
     * Create and show an alert modal
     * @param {Object} options - Modal options
     * @returns {Promise} Promise that resolves when closed
     */
    static alert(options = {}) {
        return new Promise((resolve) => {
            const modal = new Modal({
                id: 'alert-modal',
                title: options.title || 'Alert',
                size: options.size || 'sm'
            });
            
            modal.setBody(options.message || 'Alert message');
            modal.setFooter([
                {
                    text: options.buttonText || 'OK',
                    className: options.buttonClass || 'btn-primary',
                    dismiss: true
                }
            ]);
            
            // Clean up when modal is hidden
            modal.element.addEventListener('hidden.bs.modal', () => {
                modal.destroy();
                resolve();
            });
            
            modal.show();
        });
    }
}
