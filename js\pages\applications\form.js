/**
 * Application Form Module
 * 
 * Handles all form-related functionality for the applications page
 * including validation, submission, and data management.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { events, EVENT_NAMES } from '../../core/events.js';
import { state } from '../../core/state.js';
import { validation } from '../../services/validation.js';
import { createApplication, updateApplication, getApplication } from '../../services/api.js';

export class ApplicationForm {
    constructor(formId, options = {}) {
        this.formElement = typeof formId === 'string' ? document.getElementById(formId) : formId;
        this.options = {
            onSubmit: null,
            onReset: null,
            validateOnInput: true,
            ...options
        };
        
        this.isEditing = false;
        this.editingId = null;
        
        this.init();
    }
    
    /**
     * Initialize the form
     */
    init() {
        if (!this.formElement) {
            console.error('Form element not found');
            return;
        }
        
        this.setupEventListeners();
        this.setupValidation();
        this.setupTrackingToggle();
        
        console.log('Application form initialized');
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Form submission
        this.formElement.addEventListener('submit', (e) => {
            this.handleSubmit(e);
        });
        
        // Form reset
        this.formElement.addEventListener('reset', (e) => {
            this.handleReset(e);
        });
        
        // Real-time validation
        if (this.options.validateOnInput) {
            validation.setupRealTimeValidation(this.formElement);
        }
    }
    
    /**
     * Set up form validation
     */
    setupValidation() {
        // Add custom validation rules if needed
        validation.addRule('appName', (value) => {
            return value && value.length >= 2 && value.length <= 100;
        });
        
        validation.setMessage('appName', 'Application name must be between 2 and 100 characters.');
    }
    
    /**
     * Set up tracking options toggle
     */
    setupTrackingToggle() {
        const enableTrackingCheckbox = document.getElementById('enableTracking');
        const trackingOptions = document.getElementById('trackingOptions');
        
        if (enableTrackingCheckbox && trackingOptions) {
            enableTrackingCheckbox.addEventListener('change', () => {
                trackingOptions.style.display = enableTrackingCheckbox.checked ? 'block' : 'none';
            });
        }
    }
    
    /**
     * Handle form submission
     * @param {Event} event - Submit event
     */
    async handleSubmit(event) {
        event.preventDefault();
        event.stopPropagation();
        
        try {
            // Validate form
            const validationResult = validation.validateForm(this.formElement);
            
            if (!validationResult.isValid) {
                this.formElement.classList.add('was-validated');
                return;
            }
            
            // Extract form data
            const formData = this.extractFormData();
            
            // Show loading state
            this.setSubmitButtonLoading(true);
            
            // Call submit handler
            if (this.options.onSubmit) {
                await this.options.onSubmit(formData);
            }
            
        } catch (error) {
            console.error('Form submission error:', error);
            this.showError('Failed to save application. Please try again.');
        } finally {
            this.setSubmitButtonLoading(false);
        }
    }
    
    /**
     * Handle form reset
     * @param {Event} event - Reset event
     */
    handleReset(event) {
        // Clear validation state
        this.formElement.classList.remove('was-validated');
        
        // Hide tracking options
        const trackingOptions = document.getElementById('trackingOptions');
        if (trackingOptions) {
            trackingOptions.style.display = 'none';
        }
        
        // Reset editing state
        this.setEditMode(false);
        
        // Call reset handler
        if (this.options.onReset) {
            this.options.onReset();
        }
    }
    
    /**
     * Extract form data
     * @returns {Object} Structured form data
     */
    extractFormData() {
        const formData = new FormData(this.formElement);
        const data = {};
        
        // Convert form data to object
        for (let [key, value] of formData.entries()) {
            const element = this.formElement.querySelector(`[name="${key}"]`);
            if (element) {
                if (element.type === 'checkbox') {
                    data[key] = element.checked;
                } else if (key === 'track_intr') {
                    data[key] = parseInt(value) || 1;
                } else {
                    data[key] = value;
                }
            }
        }
        
        // Structure the data according to API requirements
        return {
            app_name: data.app_name,
            app_type: data.app_type,
            current_version: data.current_version,
            released_date: data.released_date,
            publisher: data.publisher,
            description: data.description || '',
            download_link: data.download_link || '',
            enable_tracking: data.enable_tracking || false,
            track: {
                usage: data.track_usage || false,
                location: data.track_location || false,
                cpu_memory: {
                    track_cm: data.track_cm || false,
                    track_intr: data.track_intr || 1
                }
            },
            registered_date: data.registered_date
        };
    }

    /**
     * Create new application
     * @param {Object} applicationData - Application data
     * @returns {Promise} API response
     */
    async createApplication(applicationData) {
        try {
            const result = await createApplication(applicationData);
            events.emit('applications:created', result);
            return result;
        } catch (error) {
            console.error('Error creating application:', error);
            throw error;
        }
    }

    /**
     * Update existing application
     * @param {number} id - Application ID
     * @param {Object} applicationData - Application data
     * @returns {Promise} API response
     */
    async updateApplication(id, applicationData) {
        try {
            const result = await updateApplication(id, applicationData);
            events.emit('applications:updated', { id, data: result });
            return result;
        } catch (error) {
            console.error('Error updating application:', error);
            throw error;
        }
    }

    /**
     * Load application data for editing
     * @param {number} id - Application ID
     */
    async loadApplicationForEdit(id) {
        try {
            this.setLoadingState(true);

            const application = await getApplication(id);
            this.populateForm(application);
            this.setEditMode(true, id);

            events.emit('applications:loaded_for_edit', { id, data: application });

        } catch (error) {
            console.error('Error loading application for edit:', error);
            this.showError('Failed to load application data.');
            throw error;
        } finally {
            this.setLoadingState(false);
        }
    }

    /**
     * Populate form with application data
     * @param {Object} app - Application data
     */
    populateForm(app) {
        // Basic fields mapping
        const fieldMappings = {
            'appName': app.app_name,
            'appType': app.app_type,
            'currentVersion': app.current_version,
            'releasedDate': app.released_date,
            'publisher': app.publisher,
            'description': app.description || '',
            'downloadLink': app.download_link || '',
            'registeredDate': app.registered_date
        };

        // Populate basic fields
        Object.entries(fieldMappings).forEach(([fieldId, value]) => {
            const element = document.getElementById(fieldId);
            if (element) {
                element.value = value || '';
            }
        });

        // Handle tracking fields
        const enableTrackingCheckbox = document.getElementById('enableTracking');
        if (enableTrackingCheckbox) {
            enableTrackingCheckbox.checked = app.enable_tracking || false;
            enableTrackingCheckbox.dispatchEvent(new Event('change')); // Trigger toggle
        }

        if (app.track) {
            this.setCheckboxValue('trackUsage', app.track.usage);
            this.setCheckboxValue('trackLocation', app.track.location);

            if (app.track.cpu_memory) {
                this.setCheckboxValue('trackCpuMemory', app.track.cpu_memory.track_cm);
                this.setFieldValue('trackInterval', app.track.cpu_memory.track_intr || 1);
            }
        }
    }

    /**
     * Set checkbox value safely
     * @param {string} id - Element ID
     * @param {boolean} value - Checkbox value
     */
    setCheckboxValue(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.checked = Boolean(value);
        }
    }

    /**
     * Set field value safely
     * @param {string} id - Element ID
     * @param {*} value - Field value
     */
    setFieldValue(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.value = value || '';
        }
    }

    /**
     * Set edit mode
     * @param {boolean} isEditing - Whether in edit mode
     * @param {number} id - Application ID (when editing)
     */
    setEditMode(isEditing, id = null) {
        this.isEditing = isEditing;
        this.editingId = id;

        const submitBtn = this.formElement.querySelector('button[type="submit"]');
        if (submitBtn) {
            if (isEditing) {
                submitBtn.innerHTML = '<i class="bi bi-check-circle me-1"></i>Update Application';
            } else {
                submitBtn.innerHTML = '<i class="bi bi-check-circle me-1"></i>Save Application';
            }
        }

        // Update state
        state.set('applications.editingApplicationId', id);
    }

    /**
     * Set submit button loading state
     * @param {boolean} loading - Loading state
     */
    setSubmitButtonLoading(loading) {
        const submitBtn = this.formElement.querySelector('button[type="submit"]');
        if (submitBtn) {
            if (loading) {
                const text = this.isEditing ? 'Updating...' : 'Saving...';
                submitBtn.innerHTML = `<span class="spinner-border spinner-border-sm me-1"></span>${text}`;
                submitBtn.disabled = true;
            } else {
                const text = this.isEditing ? 'Update Application' : 'Save Application';
                submitBtn.innerHTML = `<i class="bi bi-check-circle me-1"></i>${text}`;
                submitBtn.disabled = false;
            }
        }
    }

    /**
     * Set form loading state
     * @param {boolean} loading - Loading state
     */
    setLoadingState(loading) {
        const formElements = this.formElement.querySelectorAll('input, select, textarea, button');
        formElements.forEach(element => {
            element.disabled = loading;
        });
    }

    /**
     * Show error message
     * @param {string} message - Error message
     */
    showError(message) {
        // For now use alert, can be replaced with toast notifications
        alert(message);
    }

    /**
     * Reset form to initial state
     */
    reset() {
        this.formElement.reset();
        this.handleReset(new Event('reset'));
    }

    /**
     * Update form from state
     * @param {Object} applicationsState - Applications state
     */
    updateFromState(applicationsState) {
        // Update edit mode based on state
        const editingId = applicationsState.editingApplicationId;
        if (editingId !== this.editingId) {
            this.setEditMode(Boolean(editingId), editingId);
        }
    }

    /**
     * Destroy the form component
     */
    destroy() {
        // Remove event listeners and cleanup
        if (this.formElement) {
            this.formElement.removeEventListener('submit', this.handleSubmit);
            this.formElement.removeEventListener('reset', this.handleReset);
        }

        console.log('Application form destroyed');
    }
}
