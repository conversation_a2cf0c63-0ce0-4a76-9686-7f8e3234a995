/**
 * Application Form Component
 * 
 * Handles application form rendering, validation, and submission
 * Provides event-driven interface for form interactions
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { ApplicationValidation } from '../../services/validation.js';
import { EventEmitter } from '../../core/events.js';
import { formatDate } from '../../utils/helpers.js';

/**
 * Application Form Component Class
 * Manages form state, validation, and user interactions
 */
export class ApplicationForm extends EventEmitter {
    constructor(container) {
        super();
        this.container = container;
        this.form = null;
        this.isVisible = false;
        this.currentData = null;
        this.initialized = false;
    }
    
    /**
     * Initialize the form component
     */
    async init() {
        if (this.initialized) return;
        
        try {
            console.log('Initializing ApplicationForm component...');
            console.log('Container:', this.container);
            console.log('Container innerHTML before:', this.container.innerHTML);
            
            // Always create form HTML to ensure proper structure
            console.log('Creating form HTML...');
            await this.createFormHTML();
            console.log('Form HTML created. Container innerHTML after:', this.container.innerHTML);
            
            // Get form element
            console.log('Looking for form element...');
            this.form = this.container.querySelector('form');
            console.log('Form element found:', this.form);
            
            if (!this.form) {
                console.error('Available elements in container:', this.container.querySelectorAll('*'));
                throw new Error('Form element not found in container');
            }
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Initially hide the form
            this.hide();
            
            this.initialized = true;
            console.log('ApplicationForm component initialized successfully');
            
        } catch (error) {
            console.error('Error initializing ApplicationForm:', error);
            console.error('Container state:', this.container);
            console.error('Container innerHTML:', this.container.innerHTML);
            throw error;
        }
    }
    
    /**
     * Create form HTML structure
     */
    async createFormHTML() {
        this.container.innerHTML = `
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <span class="form-title">Add New Application</span>
                    </h5>
                </div>
                <div class="card-body">
                    <form id="applicationFormElement" novalidate>
                        <input type="hidden" name="id" id="applicationId">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="app_name" class="form-label">Application Name *</label>
                                    <input type="text" class="form-control" id="app_name" name="app_name" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="app_type" class="form-label">Application Type *</label>
                                    <select class="form-select" id="app_type" name="app_type" required>
                                        <option value="">Select Type</option>
                                        <option value="Web Application">Web Application</option>
                                        <option value="Mobile App">Mobile App</option>
                                        <option value="Desktop Application">Desktop Application</option>
                                        <option value="API/Service">API/Service</option>
                                        <option value="Other">Other</option>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="current_version" class="form-label">Current Version *</label>
                                    <input type="text" class="form-control" id="current_version" name="current_version" 
                                           placeholder="e.g., 1.0.0" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="released_date" class="form-label">Released Date *</label>
                                    <input type="date" class="form-control" id="released_date" name="released_date" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="publisher" class="form-label">Publisher *</label>
                                    <input type="text" class="form-control" id="publisher" name="publisher" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="registered_date" class="form-label">Registered Date *</label>
                                    <input type="date" class="form-control" id="registered_date" name="registered_date" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="Brief description of the application"></textarea>
                            <div class="invalid-feedback"></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="download_link" class="form-label">Download Link</label>
                            <input type="url" class="form-control" id="download_link" name="download_link" 
                                   placeholder="https://example.com/download">
                            <div class="invalid-feedback"></div>
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-secondary" id="cancelBtn">Cancel</button>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <span class="submit-text">Save Application</span>
                                <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Form submission
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        
        // Cancel button
        const cancelBtn = this.form.querySelector('#cancelBtn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.handleCancel());
        }
        
        // Real-time validation
        this.form.addEventListener('input', (e) => this.handleInput(e));
        this.form.addEventListener('change', (e) => this.handleInput(e));
        
        // Set default registered date to today
        const registeredDateInput = this.form.querySelector('#registered_date');
        if (registeredDateInput && !registeredDateInput.value) {
            registeredDateInput.value = formatDate(new Date(), 'iso');
        }
    }
    
    /**
     * Handle form submission
     * @param {Event} e - Submit event
     */
    async handleSubmit(e) {
        e.preventDefault();
        
        try {
            // Show loading state
            this.setLoading(true);
            
            // Validate form
            const validation = ApplicationValidation.validateForm(this.form);
            
            if (!validation.isValid) {
                this.displayValidationErrors(validation.errors);
                return;
            }
            
            // Clear any previous errors
            this.clearValidationErrors();
            
            // Get form data
            const formData = new FormData(this.form);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value.trim();
            }
            
            // Convert empty strings to null for optional fields
            ['description', 'download_link'].forEach(field => {
                if (data[field] === '') {
                    data[field] = null;
                }
            });
            
            // Store current data
            this.currentData = data;
            
            // Emit submit event
            this.emit('submit', data);
            
        } catch (error) {
            console.error('Error submitting form:', error);
            this.showError('An error occurred while submitting the form.');
        } finally {
            this.setLoading(false);
        }
    }
    
    /**
     * Handle cancel action
     */
    handleCancel() {
        this.emit('cancel');
    }
    
    /**
     * Handle input changes for real-time validation
     * @param {Event} e - Input event
     */
    handleInput(e) {
        const field = e.target;
        const fieldName = field.name;
        
        if (!fieldName) return;
        
        // Clear previous error for this field
        this.clearFieldError(field);
        
        // Validate single field
        const validation = ApplicationValidation.validate(
            { [fieldName]: field.value },
            { [fieldName]: ApplicationValidation.rules[fieldName] }
        );
        
        if (!validation.isValid && validation.errors[fieldName]) {
            this.showFieldError(field, validation.errors[fieldName][0]);
        }
    }
    
    /**
     * Show the form
     */
    show() {
        this.container.style.display = 'block';
        this.isVisible = true;
        
        // Focus on first input
        const firstInput = this.form.querySelector('input[type="text"]');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
        
        this.emit('show');
    }
    
    /**
     * Hide the form
     */
    hide() {
        this.container.style.display = 'none';
        this.isVisible = false;
        this.emit('hide');
    }
    
    /**
     * Check if form is visible
     * @returns {boolean} Visibility status
     */
    isFormVisible() {
        return this.isVisible;
    }
    
    /**
     * Reset form to initial state
     */
    reset() {
        if (this.form) {
            this.form.reset();
            this.clearValidationErrors();
            
            // Reset title
            const title = this.container.querySelector('.form-title');
            if (title) {
                title.textContent = 'Add New Application';
            }
            
            // Reset submit button
            const submitBtn = this.form.querySelector('#submitBtn .submit-text');
            if (submitBtn) {
                submitBtn.textContent = 'Save Application';
            }
            
            // Set default registered date
            const registeredDateInput = this.form.querySelector('#registered_date');
            if (registeredDateInput) {
                registeredDateInput.value = formatDate(new Date(), 'iso');
            }
        }
        
        this.currentData = null;
        this.emit('reset');
    }
    
    /**
     * Populate form with data
     * @param {Object} data - Data to populate
     */
    populate(data) {
        if (!data || !this.form) return;
        
        // Update title for editing
        const title = this.container.querySelector('.form-title');
        if (title) {
            title.textContent = 'Edit Application';
        }
        
        // Update submit button text
        const submitBtn = this.form.querySelector('#submitBtn .submit-text');
        if (submitBtn) {
            submitBtn.textContent = 'Update Application';
        }
        
        // Populate form fields
        Object.keys(data).forEach(key => {
            const field = this.form.querySelector(`[name="${key}"]`);
            if (field) {
                if (field.type === 'date' && data[key]) {
                    // Format date for input
                    field.value = formatDate(data[key], 'iso');
                } else {
                    field.value = data[key] || '';
                }
            }
        });
        
        this.currentData = data;
        this.clearValidationErrors();
        this.emit('populate', data);
    }
    
    /**
     * Get current form data
     * @returns {Object} Current form data
     */
    getData() {
        if (!this.form) return null;
        
        const formData = new FormData(this.form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            data[key] = value.trim();
        }
        
        return data;
    }
    
    /**
     * Set loading state
     * @param {boolean} loading - Loading state
     */
    setLoading(loading) {
        const submitBtn = this.form.querySelector('#submitBtn');
        const spinner = submitBtn.querySelector('.spinner-border');
        const text = submitBtn.querySelector('.submit-text');
        
        if (loading) {
            submitBtn.disabled = true;
            spinner.classList.remove('d-none');
            text.style.opacity = '0.7';
        } else {
            submitBtn.disabled = false;
            spinner.classList.add('d-none');
            text.style.opacity = '1';
        }
    }
    
    /**
     * Display validation errors
     * @param {Object} errors - Validation errors
     */
    displayValidationErrors(errors) {
        Object.keys(errors).forEach(fieldName => {
            const field = this.form.querySelector(`[name="${fieldName}"]`);
            if (field && errors[fieldName].length > 0) {
                this.showFieldError(field, errors[fieldName][0]);
            }
        });
    }
    
    /**
     * Clear all validation errors
     */
    clearValidationErrors() {
        this.form.querySelectorAll('.is-invalid').forEach(field => {
            field.classList.remove('is-invalid');
        });
        
        this.form.querySelectorAll('.invalid-feedback').forEach(feedback => {
            feedback.textContent = '';
        });
    }
    
    /**
     * Show field error
     * @param {HTMLElement} field - Form field
     * @param {string} message - Error message
     */
    showFieldError(field, message) {
        field.classList.add('is-invalid');
        
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.textContent = message;
        }
    }
    
    /**
     * Clear field error
     * @param {HTMLElement} field - Form field
     */
    clearFieldError(field) {
        field.classList.remove('is-invalid');
        
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.textContent = '';
        }
    }
    
    /**
     * Show general error message
     * @param {string} message - Error message
     */
    showError(message) {
        // Create error alert
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible fade show';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Insert at top of form
        this.form.insertBefore(alert, this.form.firstChild);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
    
    /**
     * Destroy the component
     */
    async destroy() {
        try {
            console.log('Destroying ApplicationForm component...');
            
            // Remove event listeners
            if (this.form) {
                this.form.removeEventListener('submit', this.handleSubmit);
                this.form.removeEventListener('input', this.handleInput);
                this.form.removeEventListener('change', this.handleInput);
            }
            
            // Clear container
            if (this.container) {
                this.container.innerHTML = '';
            }
            
            // Reset state
            this.form = null;
            this.currentData = null;
            this.isVisible = false;
            this.initialized = false;
            
            // Remove all event listeners
            this.removeAllListeners();
            
            console.log('ApplicationForm component destroyed');
            
        } catch (error) {
            console.error('Error destroying ApplicationForm:', error);
        }
    }
}
