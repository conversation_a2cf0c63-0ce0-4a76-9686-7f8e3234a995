/**
 * Charts Module
 * 
 * Enhanced chart management with theme integration,
 * responsive design, and multiple chart types support.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { events, EVENT_NAMES } from '../core/events.js';
import { state } from '../core/state.js';
import { getChartData } from '../services/api.js';

class ChartManager {
    constructor() {
        this.charts = new Map();
        this.defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                }
            },
            scales: {
                x: {
                    display: true,
                    grid: {
                        display: true
                    }
                },
                y: {
                    display: true,
                    beginAtZero: true,
                    grid: {
                        display: true
                    }
                }
            }
        };
        
        this.themeColors = {
            light: {
                primary: '#0d6efd',
                secondary: '#6c757d',
                success: '#198754',
                danger: '#dc3545',
                warning: '#ffc107',
                info: '#0dcaf0',
                background: '#ffffff',
                text: '#212529',
                grid: '#dee2e6'
            },
            dark: {
                primary: '#0d6efd',
                secondary: '#6c757d',
                success: '#198754',
                danger: '#dc3545',
                warning: '#ffc107',
                info: '#0dcaf0',
                background: '#212529',
                text: '#ffffff',
                grid: '#495057'
            }
        };
        
        this.setupThemeListener();
    }
    
    /**
     * Set up theme change listener
     */
    setupThemeListener() {
        events.on(EVENT_NAMES.THEME_CHANGED, (theme) => {
            this.updateChartsTheme(theme);
        });
    }
    
    /**
     * Create a chart
     * @param {string|HTMLElement} canvas - Canvas element or ID
     * @param {Object} config - Chart configuration
     * @returns {Chart} Chart instance
     */
    createChart(canvas, config) {
        const canvasElement = typeof canvas === 'string' ? document.getElementById(canvas) : canvas;
        
        if (!canvasElement) {
            console.error('Canvas element not found');
            return null;
        }
        
        // Apply theme-aware styling
        const themedConfig = this.applyTheme(config);
        
        // Create chart
        const chart = new Chart(canvasElement, themedConfig);
        
        // Store chart reference
        const chartId = canvasElement.id || `chart_${Date.now()}`;
        this.charts.set(chartId, chart);
        
        return chart;
    }
    
    /**
     * Apply theme to chart configuration
     * @param {Object} config - Chart configuration
     * @returns {Object} Themed configuration
     */
    applyTheme(config) {
        const currentTheme = state.get('theme') || 'light';
        const effectiveTheme = currentTheme === 'auto' ? this.getSystemTheme() : currentTheme;
        const colors = this.themeColors[effectiveTheme] || this.themeColors.light;
        
        // Deep clone config to avoid mutations
        const themedConfig = JSON.parse(JSON.stringify(config));
        
        // Apply theme colors to options
        if (!themedConfig.options) {
            themedConfig.options = {};
        }
        
        // Merge with default options
        themedConfig.options = this.mergeDeep(this.defaultOptions, themedConfig.options);
        
        // Apply theme colors
        if (themedConfig.options.plugins?.legend?.labels) {
            themedConfig.options.plugins.legend.labels.color = colors.text;
        }
        
        if (themedConfig.options.scales) {
            Object.keys(themedConfig.options.scales).forEach(scaleKey => {
                const scale = themedConfig.options.scales[scaleKey];
                if (scale.ticks) {
                    scale.ticks.color = colors.text;
                }
                if (scale.grid) {
                    scale.grid.color = colors.grid;
                }
                if (scale.title) {
                    scale.title.color = colors.text;
                }
            });
        }
        
        // Apply theme colors to datasets
        if (themedConfig.data?.datasets) {
            themedConfig.data.datasets.forEach((dataset, index) => {
                if (!dataset.backgroundColor && !dataset.borderColor) {
                    const colorKeys = Object.keys(colors).filter(key => 
                        !['background', 'text', 'grid'].includes(key)
                    );
                    const colorKey = colorKeys[index % colorKeys.length];
                    
                    dataset.backgroundColor = colors[colorKey] + '20'; // Add transparency
                    dataset.borderColor = colors[colorKey];
                }
            });
        }
        
        return themedConfig;
    }
    
    /**
     * Load and display dashboard chart
     */
    async loadDashboardChart() {
        try {
            const chartCanvas = document.getElementById('myChart');
            if (!chartCanvas) {
                console.warn('Chart canvas not found');
                return;
            }
            
            // Show loading state
            this.showChartLoading(chartCanvas, true);
            
            // Fetch chart data
            const data = await getChartData();
            
            // Create chart configuration
            const config = {
                type: 'bar',
                data: {
                    labels: data.labels,
                    datasets: [{
                        label: '# of Votes',
                        data: data.values,
                        borderWidth: 1
                    }]
                },
                options: {
                    plugins: {
                        title: {
                            display: true,
                            text: 'Dashboard Statistics'
                        }
                    }
                }
            };
            
            // Create chart
            const chart = this.createChart(chartCanvas, config);
            
            // Hide loading state
            this.showChartLoading(chartCanvas, false);
            
            events.emit('chart:loaded', { id: 'myChart', chart });
            
            return chart;
            
        } catch (error) {
            console.error('Error loading dashboard chart:', error);
            this.showChartError(document.getElementById('myChart'), error.message);
        }
    }
    
    /**
     * Update all charts with new theme
     * @param {string} theme - Theme name
     */
    updateChartsTheme(theme) {
        this.charts.forEach((chart, chartId) => {
            try {
                // Get current config
                const currentConfig = chart.config;
                
                // Apply new theme
                const themedConfig = this.applyTheme(currentConfig);
                
                // Update chart options
                chart.options = themedConfig.options;
                
                // Update chart
                chart.update('none'); // No animation for theme changes
                
            } catch (error) {
                console.error(`Error updating chart theme for ${chartId}:`, error);
            }
        });
    }
    
    /**
     * Show chart loading state
     * @param {HTMLElement} canvas - Canvas element
     * @param {boolean} loading - Loading state
     */
    showChartLoading(canvas, loading) {
        if (!canvas) return;
        
        const container = canvas.parentElement;
        let loadingElement = container.querySelector('.chart-loading');
        
        if (loading) {
            if (!loadingElement) {
                loadingElement = document.createElement('div');
                loadingElement.className = 'chart-loading position-absolute top-50 start-50 translate-middle';
                loadingElement.innerHTML = `
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                `;
                container.style.position = 'relative';
                container.appendChild(loadingElement);
            }
            canvas.style.opacity = '0.3';
        } else {
            if (loadingElement) {
                loadingElement.remove();
            }
            canvas.style.opacity = '1';
        }
    }
    
    /**
     * Show chart error state
     * @param {HTMLElement} canvas - Canvas element
     * @param {string} message - Error message
     */
    showChartError(canvas, message) {
        if (!canvas) return;
        
        const container = canvas.parentElement;
        
        // Remove loading state
        this.showChartLoading(canvas, false);
        
        // Show error message
        const errorElement = document.createElement('div');
        errorElement.className = 'chart-error position-absolute top-50 start-50 translate-middle text-center';
        errorElement.innerHTML = `
            <div class="text-danger">
                <i class="bi bi-exclamation-triangle fs-1 d-block mb-2"></i>
                <p class="mb-0">Failed to load chart</p>
                <small class="text-muted">${message}</small>
            </div>
        `;
        
        container.style.position = 'relative';
        container.appendChild(errorElement);
        canvas.style.opacity = '0.3';
    }
    
    /**
     * Get system theme preference
     * @returns {string} System theme
     */
    getSystemTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }
        return 'light';
    }
    
    /**
     * Deep merge objects
     * @param {Object} target - Target object
     * @param {Object} source - Source object
     * @returns {Object} Merged object
     */
    mergeDeep(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.mergeDeep(result[key] || {}, source[key]);
            } else {
                result[key] = source[key];
            }
        }
        
        return result;
    }
    
    /**
     * Destroy a chart
     * @param {string} chartId - Chart ID
     */
    destroyChart(chartId) {
        const chart = this.charts.get(chartId);
        if (chart) {
            chart.destroy();
            this.charts.delete(chartId);
        }
    }
    
    /**
     * Destroy all charts
     */
    destroyAllCharts() {
        this.charts.forEach((chart, chartId) => {
            chart.destroy();
        });
        this.charts.clear();
    }
    
    /**
     * Get chart by ID
     * @param {string} chartId - Chart ID
     * @returns {Chart|null} Chart instance
     */
    getChart(chartId) {
        return this.charts.get(chartId) || null;
    }
    
    /**
     * Get all charts
     * @returns {Map} Charts map
     */
    getAllCharts() {
        return new Map(this.charts);
    }
}

// Create singleton instance
const chartManager = new ChartManager();

// Export load function for compatibility
export function loadChart() {
    return chartManager.loadDashboardChart();
}

// Export chart manager for advanced usage
export { chartManager };
