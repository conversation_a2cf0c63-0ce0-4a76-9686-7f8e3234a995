/**
 * Application Constants
 * 
 * Centralized constants for the application including
 * API endpoints, configuration values, and enums.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

// API Configuration
export const API = {
    BASE_URL: 'http://127.0.0.1:8000/api',
    TIMEOUT: 30000, // 30 seconds
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000 // 1 second
};

// Application Configuration
export const APP = {
    NAME: 'Pure Bootstrap Admin Template',
    VERSION: '1.0.0',
    AUTHOR: 'Admin Panel System',
    DESCRIPTION: 'A clean and modern admin template built with Bootstrap 5'
};

// Storage Keys
export const STORAGE_KEYS = {
    THEME: 'theme',
    USER_PREFERENCES: 'user_preferences',
    APPLICATIONS_FILTERS: 'applications_filters',
    TABLE_SETTINGS: 'table_settings'
};

// Theme Constants
export const THEMES = {
    LIGHT: 'light',
    DARK: 'dark',
    AUTO: 'auto'
};

// Application Types
export const APPLICATION_TYPES = {
    WEB: 'Web Application',
    MOBILE: 'Mobile Application',
    DESKTOP: 'Desktop Application',
    API: 'API Service',
    LIBRARY: 'Library',
    FRAMEWORK: 'Framework',
    TOOL: 'Development Tool',
    OTHER: 'Other'
};

// Pagination Constants
export const PAGINATION = {
    DEFAULT_PAGE_SIZE: 10,
    PAGE_SIZE_OPTIONS: [5, 10, 25, 50, 100],
    MAX_VISIBLE_PAGES: 5
};

// Validation Constants
export const VALIDATION = {
    MIN_APP_NAME_LENGTH: 2,
    MAX_APP_NAME_LENGTH: 100,
    MIN_DESCRIPTION_LENGTH: 0,
    MAX_DESCRIPTION_LENGTH: 1000,
    MIN_VERSION_LENGTH: 1,
    MAX_VERSION_LENGTH: 20,
    MIN_PUBLISHER_LENGTH: 1,
    MAX_PUBLISHER_LENGTH: 100
};

// Date Formats
export const DATE_FORMATS = {
    DISPLAY: 'MMM DD, YYYY',
    INPUT: 'YYYY-MM-DD',
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    ISO: 'YYYY-MM-DDTHH:mm:ss.sssZ'
};

// HTTP Status Codes
export const HTTP_STATUS = {
    OK: 200,
    CREATED: 201,
    NO_CONTENT: 204,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    METHOD_NOT_ALLOWED: 405,
    CONFLICT: 409,
    UNPROCESSABLE_ENTITY: 422,
    INTERNAL_SERVER_ERROR: 500,
    BAD_GATEWAY: 502,
    SERVICE_UNAVAILABLE: 503,
    GATEWAY_TIMEOUT: 504
};

// Error Messages
export const ERROR_MESSAGES = {
    NETWORK_ERROR: 'Network error. Please check your connection and try again.',
    SERVER_ERROR: 'Server error. Please try again later.',
    VALIDATION_ERROR: 'Please check your input and try again.',
    NOT_FOUND: 'The requested resource was not found.',
    UNAUTHORIZED: 'You are not authorized to perform this action.',
    FORBIDDEN: 'Access to this resource is forbidden.',
    TIMEOUT: 'Request timed out. Please try again.',
    UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.'
};

// Success Messages
export const SUCCESS_MESSAGES = {
    APPLICATION_CREATED: 'Application created successfully!',
    APPLICATION_UPDATED: 'Application updated successfully!',
    APPLICATION_DELETED: 'Application deleted successfully!',
    SETTINGS_SAVED: 'Settings saved successfully!',
    DATA_EXPORTED: 'Data exported successfully!',
    DATA_IMPORTED: 'Data imported successfully!'
};

// UI Constants
export const UI = {
    DEBOUNCE_DELAY: 300,
    ANIMATION_DURATION: 300,
    TOAST_DURATION: 5000,
    LOADING_MIN_DURATION: 500,
    MODAL_BACKDROP_TRANSITION: 150
};

// Chart Colors
export const CHART_COLORS = {
    PRIMARY: '#0d6efd',
    SECONDARY: '#6c757d',
    SUCCESS: '#198754',
    DANGER: '#dc3545',
    WARNING: '#ffc107',
    INFO: '#0dcaf0',
    LIGHT: '#f8f9fa',
    DARK: '#212529'
};

// Bootstrap Breakpoints
export const BREAKPOINTS = {
    XS: 0,
    SM: 576,
    MD: 768,
    LG: 992,
    XL: 1200,
    XXL: 1400
};

// File Upload Constants
export const FILE_UPLOAD = {
    MAX_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
    ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.pdf']
};

// Regular Expressions
export const REGEX = {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
    VERSION: /^\d+(\.\d+)*(-[a-zA-Z0-9]+)?$/,
    PHONE: /^[\+]?[1-9][\d]{0,15}$/,
    ALPHANUMERIC: /^[a-zA-Z0-9]+$/,
    SLUG: /^[a-z0-9]+(?:-[a-z0-9]+)*$/
};

// Event Names (extending core events)
export const CUSTOM_EVENTS = {
    // Application specific events
    APPLICATION_FORM_CHANGED: 'application:form:changed',
    APPLICATION_VALIDATION_FAILED: 'application:validation:failed',
    
    // Table events
    TABLE_ROW_CLICKED: 'table:row:clicked',
    TABLE_HEADER_CLICKED: 'table:header:clicked',
    TABLE_FILTER_CHANGED: 'table:filter:changed',
    
    // Search events
    SEARCH_QUERY_CHANGED: 'search:query:changed',
    SEARCH_FILTER_APPLIED: 'search:filter:applied',
    SEARCH_RESULTS_UPDATED: 'search:results:updated',
    
    // UI events
    SIDEBAR_COLLAPSED: 'ui:sidebar:collapsed',
    SIDEBAR_EXPANDED: 'ui:sidebar:expanded',
    MODAL_OPENED: 'ui:modal:opened',
    MODAL_CLOSED: 'ui:modal:closed'
};

// Feature Flags
export const FEATURES = {
    DARK_MODE: true,
    NOTIFICATIONS: true,
    CHARTS: true,
    EXPORT_DATA: true,
    IMPORT_DATA: true,
    BULK_OPERATIONS: true,
    ADVANCED_SEARCH: true,
    USER_PREFERENCES: true
};

// Performance Constants
export const PERFORMANCE = {
    VIRTUAL_SCROLL_THRESHOLD: 100,
    LAZY_LOAD_THRESHOLD: 50,
    CACHE_TTL: 5 * 60 * 1000, // 5 minutes
    MAX_CACHE_SIZE: 100
};

// Security Constants
export const SECURITY = {
    MAX_LOGIN_ATTEMPTS: 5,
    SESSION_TIMEOUT: 30 * 60 * 1000, // 30 minutes
    CSRF_TOKEN_HEADER: 'X-CSRF-Token',
    API_KEY_HEADER: 'X-API-Key'
};

// Development Constants
export const DEV = {
    DEBUG: process?.env?.NODE_ENV === 'development',
    LOG_LEVEL: 'info',
    MOCK_API: false,
    ENABLE_DEVTOOLS: true
};

// Export all constants as a single object for convenience
export const CONSTANTS = {
    API,
    APP,
    STORAGE_KEYS,
    THEMES,
    APPLICATION_TYPES,
    PAGINATION,
    VALIDATION,
    DATE_FORMATS,
    HTTP_STATUS,
    ERROR_MESSAGES,
    SUCCESS_MESSAGES,
    UI,
    CHART_COLORS,
    BREAKPOINTS,
    FILE_UPLOAD,
    REGEX,
    CUSTOM_EVENTS,
    FEATURES,
    PERFORMANCE,
    SECURITY,
    DEV
};
