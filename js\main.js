/**
 * Legacy Main.js - Compatibility Layer
 *
 * This file provides backward compatibility for the old module structure.
 * The new modular structure is now handled by js/core/app.js
 *
 * @deprecated Use js/core/app.js instead
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

console.warn('⚠️  js/main.js is deprecated. The application now uses the new modular structure.');
console.log('ℹ️  Main application logic has moved to js/core/app.js');

// Re-export functions for backward compatibility
export { router as loadPage } from './core/router.js';
export { initializeTheme } from './ui/theme.js';
export { loadChart } from './ui/charts.js';

// Legacy initialization (if needed)
document.addEventListener('DOMContentLoaded', () => {
    console.log('🔄 Legacy main.js compatibility layer loaded');
    console.log('✨ New modular structure is handling initialization');
});
