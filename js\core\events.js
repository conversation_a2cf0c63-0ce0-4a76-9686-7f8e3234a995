/**
 * Event System Module
 * 
 * Provides a centralized event system for loose coupling between modules
 * using the publish-subscribe pattern.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

class EventBus {
    constructor() {
        this.events = new Map();
        this.onceEvents = new Map();
    }
    
    /**
     * Subscribe to an event
     * @param {string} eventName - Name of the event
     * @param {Function} callback - Callback function
     * @returns {Function} Unsubscribe function
     */
    on(eventName, callback) {
        if (!this.events.has(eventName)) {
            this.events.set(eventName, new Set());
        }
        
        this.events.get(eventName).add(callback);
        
        // Return unsubscribe function
        return () => this.off(eventName, callback);
    }
    
    /**
     * Subscribe to an event that will only fire once
     * @param {string} eventName - Name of the event
     * @param {Function} callback - Callback function
     * @returns {Function} Unsubscribe function
     */
    once(eventName, callback) {
        if (!this.onceEvents.has(eventName)) {
            this.onceEvents.set(eventName, new Set());
        }
        
        this.onceEvents.get(eventName).add(callback);
        
        // Return unsubscribe function
        return () => {
            const onceListeners = this.onceEvents.get(eventName);
            if (onceListeners) {
                onceListeners.delete(callback);
            }
        };
    }
    
    /**
     * Unsubscribe from an event
     * @param {string} eventName - Name of the event
     * @param {Function} callback - Callback function to remove
     */
    off(eventName, callback) {
        const listeners = this.events.get(eventName);
        if (listeners) {
            listeners.delete(callback);
            if (listeners.size === 0) {
                this.events.delete(eventName);
            }
        }
    }
    
    /**
     * Emit an event
     * @param {string} eventName - Name of the event
     * @param {...*} args - Arguments to pass to callbacks
     */
    emit(eventName, ...args) {
        // Handle regular listeners
        const listeners = this.events.get(eventName);
        if (listeners) {
            listeners.forEach(callback => {
                try {
                    callback(...args);
                } catch (error) {
                    console.error(`Error in event listener for '${eventName}':`, error);
                }
            });
        }
        
        // Handle once listeners
        const onceListeners = this.onceEvents.get(eventName);
        if (onceListeners) {
            onceListeners.forEach(callback => {
                try {
                    callback(...args);
                } catch (error) {
                    console.error(`Error in once event listener for '${eventName}':`, error);
                }
            });
            // Clear once listeners after execution
            this.onceEvents.delete(eventName);
        }
    }
    
    /**
     * Remove all listeners for an event
     * @param {string} eventName - Name of the event
     */
    removeAllListeners(eventName) {
        this.events.delete(eventName);
        this.onceEvents.delete(eventName);
    }
    
    /**
     * Get the number of listeners for an event
     * @param {string} eventName - Name of the event
     * @returns {number} Number of listeners
     */
    listenerCount(eventName) {
        const regularCount = this.events.get(eventName)?.size || 0;
        const onceCount = this.onceEvents.get(eventName)?.size || 0;
        return regularCount + onceCount;
    }
    
    /**
     * Get all event names that have listeners
     * @returns {string[]} Array of event names
     */
    eventNames() {
        const regularEvents = Array.from(this.events.keys());
        const onceEvents = Array.from(this.onceEvents.keys());
        return [...new Set([...regularEvents, ...onceEvents])];
    }
    
    /**
     * Clear all events and listeners
     */
    clear() {
        this.events.clear();
        this.onceEvents.clear();
    }
}

// Create and export singleton instance
export const events = new EventBus();

// Common event names as constants
export const EVENT_NAMES = {
    // Navigation events
    PAGE_CHANGED: 'page:changed',
    PAGE_LOADING: 'page:loading',
    PAGE_LOADED: 'page:loaded',
    
    // Application events
    APP_INITIALIZED: 'app:initialized',
    APP_ERROR: 'app:error',
    
    // Data events
    DATA_LOADING: 'data:loading',
    DATA_LOADED: 'data:loaded',
    DATA_ERROR: 'data:error',
    
    // UI events
    THEME_CHANGED: 'ui:theme:changed',
    SIDEBAR_TOGGLED: 'ui:sidebar:toggled',
    NOTIFICATION_ADDED: 'ui:notification:added',
    
    // Applications events
    APPLICATIONS_LOADED: 'applications:loaded',
    APPLICATION_CREATED: 'applications:created',
    APPLICATION_UPDATED: 'applications:updated',
    APPLICATION_DELETED: 'applications:deleted',
    APPLICATION_SELECTED: 'applications:selected',
    
    // Form events
    FORM_SUBMITTED: 'form:submitted',
    FORM_VALIDATED: 'form:validated',
    FORM_RESET: 'form:reset'
};
