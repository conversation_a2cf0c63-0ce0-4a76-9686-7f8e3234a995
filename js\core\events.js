/**
 * Event System / Pub-Sub
 * 
 * Provides event-driven communication between components
 * Enables loose coupling and reactive programming patterns
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

/**
 * EventBus Class
 * Manages event subscriptions and emissions
 */
export class EventBus {
    constructor() {
        this.events = new Map();
        this.onceEvents = new Map();
        this.maxListeners = 100;
        this.debug = false;
    }
    
    /**
     * Subscribe to an event
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     * @returns {Function} Unsubscribe function
     */
    on(event, callback) {
        if (typeof callback !== 'function') {
            throw new Error('Callback must be a function');
        }
        
        if (!this.events.has(event)) {
            this.events.set(event, []);
        }
        
        const listeners = this.events.get(event);
        
        // Check max listeners limit
        if (listeners.length >= this.maxListeners) {
            console.warn(`Maximum listeners (${this.maxListeners}) exceeded for event: ${event}`);
        }
        
        listeners.push(callback);
        
        if (this.debug) {
            console.log(`Event listener added for: ${event}`);
        }
        
        // Return unsubscribe function
        return () => this.off(event, callback);
    }
    
    /**
     * Subscribe to an event once (auto-unsubscribe after first emission)
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     * @returns {Function} Unsubscribe function
     */
    once(event, callback) {
        if (typeof callback !== 'function') {
            throw new Error('Callback must be a function');
        }
        
        if (!this.onceEvents.has(event)) {
            this.onceEvents.set(event, []);
        }
        
        this.onceEvents.get(event).push(callback);
        
        if (this.debug) {
            console.log(`One-time event listener added for: ${event}`);
        }
        
        // Return unsubscribe function
        return () => {
            const listeners = this.onceEvents.get(event);
            if (listeners) {
                const index = listeners.indexOf(callback);
                if (index > -1) {
                    listeners.splice(index, 1);
                }
            }
        };
    }
    
    /**
     * Unsubscribe from an event
     * @param {string} event - Event name
     * @param {Function} callback - Callback function to remove
     */
    off(event, callback) {
        // Remove from regular events
        if (this.events.has(event)) {
            const listeners = this.events.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
                if (this.debug) {
                    console.log(`Event listener removed for: ${event}`);
                }
            }
            
            // Clean up empty event arrays
            if (listeners.length === 0) {
                this.events.delete(event);
            }
        }
        
        // Remove from once events
        if (this.onceEvents.has(event)) {
            const listeners = this.onceEvents.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
            
            if (listeners.length === 0) {
                this.onceEvents.delete(event);
            }
        }
    }
    
    /**
     * Emit an event
     * @param {string} event - Event name
     * @param {...*} args - Arguments to pass to listeners
     */
    emit(event, ...args) {
        if (this.debug) {
            console.log(`Emitting event: ${event}`, args);
        }
        
        // Call regular event listeners
        if (this.events.has(event)) {
            const listeners = [...this.events.get(event)]; // Copy to avoid issues if listeners modify the array
            listeners.forEach(callback => {
                try {
                    callback(...args);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
        
        // Call once event listeners and remove them
        if (this.onceEvents.has(event)) {
            const listeners = [...this.onceEvents.get(event)];
            this.onceEvents.delete(event); // Remove all once listeners
            
            listeners.forEach(callback => {
                try {
                    callback(...args);
                } catch (error) {
                    console.error(`Error in once event listener for ${event}:`, error);
                }
            });
        }
        
        // Emit to wildcard listeners
        this.emitWildcard(event, ...args);
    }
    
    /**
     * Emit to wildcard listeners
     * @param {string} event - Event name
     * @param {...*} args - Arguments
     */
    emitWildcard(event, ...args) {
        // Check for wildcard listeners (e.g., 'user:*' matches 'user:login', 'user:logout')
        this.events.forEach((listeners, eventPattern) => {
            if (eventPattern.includes('*') && this.matchesPattern(event, eventPattern)) {
                listeners.forEach(callback => {
                    try {
                        callback(event, ...args);
                    } catch (error) {
                        console.error(`Error in wildcard event listener for ${eventPattern}:`, error);
                    }
                });
            }
        });
    }
    
    /**
     * Check if event matches pattern
     * @param {string} event - Event name
     * @param {string} pattern - Pattern with wildcards
     * @returns {boolean} True if matches
     */
    matchesPattern(event, pattern) {
        const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$');
        return regex.test(event);
    }
    
    /**
     * Remove all listeners for an event
     * @param {string} event - Event name
     */
    removeAllListeners(event) {
        if (event) {
            this.events.delete(event);
            this.onceEvents.delete(event);
            if (this.debug) {
                console.log(`All listeners removed for: ${event}`);
            }
        } else {
            // Remove all listeners for all events
            this.events.clear();
            this.onceEvents.clear();
            if (this.debug) {
                console.log('All event listeners removed');
            }
        }
    }
    
    /**
     * Get listener count for an event
     * @param {string} event - Event name
     * @returns {number} Number of listeners
     */
    listenerCount(event) {
        const regularCount = this.events.has(event) ? this.events.get(event).length : 0;
        const onceCount = this.onceEvents.has(event) ? this.onceEvents.get(event).length : 0;
        return regularCount + onceCount;
    }
    
    /**
     * Get all event names
     * @returns {Array} Array of event names
     */
    eventNames() {
        const regularEvents = Array.from(this.events.keys());
        const onceEvents = Array.from(this.onceEvents.keys());
        return [...new Set([...regularEvents, ...onceEvents])];
    }
    
    /**
     * Set maximum number of listeners per event
     * @param {number} max - Maximum listeners
     */
    setMaxListeners(max) {
        this.maxListeners = max;
    }
    
    /**
     * Enable or disable debug logging
     * @param {boolean} enabled - Debug enabled
     */
    setDebug(enabled) {
        this.debug = enabled;
    }
    
    /**
     * Create a namespaced event bus
     * @param {string} namespace - Namespace prefix
     * @returns {Object} Namespaced event methods
     */
    namespace(namespace) {
        return {
            on: (event, callback) => this.on(`${namespace}:${event}`, callback),
            once: (event, callback) => this.once(`${namespace}:${event}`, callback),
            off: (event, callback) => this.off(`${namespace}:${event}`, callback),
            emit: (event, ...args) => this.emit(`${namespace}:${event}`, ...args),
            removeAllListeners: (event) => this.removeAllListeners(event ? `${namespace}:${event}` : undefined)
        };
    }
    
    /**
     * Wait for an event to be emitted
     * @param {string} event - Event name
     * @param {number} timeout - Timeout in milliseconds (optional)
     * @returns {Promise} Promise that resolves when event is emitted
     */
    waitFor(event, timeout = null) {
        return new Promise((resolve, reject) => {
            let timeoutId = null;
            
            const cleanup = this.once(event, (...args) => {
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }
                resolve(args);
            });
            
            if (timeout) {
                timeoutId = setTimeout(() => {
                    cleanup();
                    reject(new Error(`Event ${event} timeout after ${timeout}ms`));
                }, timeout);
            }
        });
    }
}

/**
 * EventEmitter Class
 * Base class for components that need event capabilities
 * Provides standard event emitter interface using EventBus internally
 */
export class EventEmitter {
    constructor() {
        this.eventBus = new EventBus();
    }
    
    /**
     * Subscribe to an event
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     * @returns {Function} Unsubscribe function
     */
    on(event, callback) {
        return this.eventBus.on(event, callback);
    }
    
    /**
     * Subscribe to an event once
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     * @returns {Function} Unsubscribe function
     */
    once(event, callback) {
        return this.eventBus.once(event, callback);
    }
    
    /**
     * Unsubscribe from an event
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     */
    off(event, callback) {
        this.eventBus.off(event, callback);
    }
    
    /**
     * Emit an event
     * @param {string} event - Event name
     * @param {...*} args - Arguments to pass to listeners
     */
    emit(event, ...args) {
        this.eventBus.emit(event, ...args);
    }
    
    /**
     * Remove all listeners for an event or all events
     * @param {string} event - Event name (optional)
     */
    removeAllListeners(event) {
        this.eventBus.removeAllListeners(event);
    }
    
    /**
     * Get listener count for an event
     * @param {string} event - Event name
     * @returns {number} Number of listeners
     */
    listenerCount(event) {
        return this.eventBus.listenerCount(event);
    }
    
    /**
     * Get all event names
     * @returns {Array} Array of event names
     */
    eventNames() {
        return this.eventBus.eventNames();
    }
    
    /**
     * Wait for an event to be emitted
     * @param {string} event - Event name
     * @param {number} timeout - Timeout in milliseconds (optional)
     * @returns {Promise} Promise that resolves when event is emitted
     */
    waitFor(event, timeout = null) {
        return this.eventBus.waitFor(event, timeout);
    }
}

// Export a default instance
export const eventBus = new EventBus();
