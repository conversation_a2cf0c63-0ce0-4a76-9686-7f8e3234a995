/**
 * Notifications Module
 * 
 * Provides toast notifications, alerts, and other user feedback
 * with Bootstrap integration and customizable options.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { events } from '../core/events.js';
import { state } from '../core/state.js';

class NotificationManager {
    constructor() {
        this.notifications = new Map();
        this.container = null;
        this.defaultOptions = {
            type: 'info', // 'success', 'error', 'warning', 'info'
            duration: 5000,
            closable: true,
            position: 'top-end',
            animation: true
        };
        
        this.init();
    }
    
    /**
     * Initialize notification system
     */
    init() {
        this.createContainer();
        this.setupEventListeners();
        
        console.log('Notification manager initialized');
    }
    
    /**
     * Create notification container
     */
    createContainer() {
        // Remove existing container
        const existing = document.getElementById('notification-container');
        if (existing) {
            existing.remove();
        }
        
        this.container = document.createElement('div');
        this.container.id = 'notification-container';
        this.container.className = 'toast-container position-fixed top-0 end-0 p-3';
        this.container.style.zIndex = '9999';
        
        document.body.appendChild(this.container);
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Listen for global notification events
        events.on('notification:show', (data) => {
            this.show(data.message, data.options);
        });
        
        events.on('notification:success', (message) => {
            this.success(message);
        });
        
        events.on('notification:error', (message) => {
            this.error(message);
        });
        
        events.on('notification:warning', (message) => {
            this.warning(message);
        });
        
        events.on('notification:info', (message) => {
            this.info(message);
        });
    }
    
    /**
     * Show a notification
     * @param {string} message - Notification message
     * @param {Object} options - Notification options
     * @returns {string} Notification ID
     */
    show(message, options = {}) {
        const config = { ...this.defaultOptions, ...options };
        const id = this.generateId();
        
        // Create toast element
        const toast = this.createToast(id, message, config);
        
        // Add to container
        this.container.appendChild(toast);
        
        // Initialize Bootstrap toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: config.duration > 0,
            delay: config.duration
        });
        
        // Store notification
        this.notifications.set(id, {
            element: toast,
            bsToast: bsToast,
            config: config
        });
        
        // Show toast
        bsToast.show();
        
        // Auto-remove after hide
        toast.addEventListener('hidden.bs.toast', () => {
            this.remove(id);
        });
        
        // Update state
        this.updateState();
        
        events.emit('notification:shown', { id, message, config });
        
        return id;
    }
    
    /**
     * Create toast element
     * @param {string} id - Toast ID
     * @param {string} message - Message
     * @param {Object} config - Configuration
     * @returns {HTMLElement} Toast element
     */
    createToast(id, message, config) {
        const toast = document.createElement('div');
        toast.className = `toast ${config.animation ? 'fade' : ''}`;
        toast.id = id;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        
        const header = document.createElement('div');
        header.className = `toast-header bg-${this.getBootstrapColor(config.type)} text-white`;
        
        const icon = document.createElement('i');
        icon.className = `bi ${this.getIcon(config.type)} me-2`;
        
        const title = document.createElement('strong');
        title.className = 'me-auto';
        title.textContent = this.getTitle(config.type);
        
        const time = document.createElement('small');
        time.textContent = 'now';
        
        header.appendChild(icon);
        header.appendChild(title);
        header.appendChild(time);
        
        if (config.closable) {
            const closeBtn = document.createElement('button');
            closeBtn.type = 'button';
            closeBtn.className = 'btn-close btn-close-white';
            closeBtn.setAttribute('data-bs-dismiss', 'toast');
            closeBtn.setAttribute('aria-label', 'Close');
            header.appendChild(closeBtn);
        }
        
        const body = document.createElement('div');
        body.className = 'toast-body';
        body.innerHTML = message;
        
        toast.appendChild(header);
        toast.appendChild(body);
        
        return toast;
    }
    
    /**
     * Show success notification
     * @param {string} message - Message
     * @param {Object} options - Options
     * @returns {string} Notification ID
     */
    success(message, options = {}) {
        return this.show(message, { ...options, type: 'success' });
    }
    
    /**
     * Show error notification
     * @param {string} message - Message
     * @param {Object} options - Options
     * @returns {string} Notification ID
     */
    error(message, options = {}) {
        return this.show(message, { ...options, type: 'error', duration: 0 }); // Don't auto-hide errors
    }
    
    /**
     * Show warning notification
     * @param {string} message - Message
     * @param {Object} options - Options
     * @returns {string} Notification ID
     */
    warning(message, options = {}) {
        return this.show(message, { ...options, type: 'warning' });
    }
    
    /**
     * Show info notification
     * @param {string} message - Message
     * @param {Object} options - Options
     * @returns {string} Notification ID
     */
    info(message, options = {}) {
        return this.show(message, { ...options, type: 'info' });
    }
    
    /**
     * Remove notification
     * @param {string} id - Notification ID
     */
    remove(id) {
        const notification = this.notifications.get(id);
        if (notification) {
            notification.element.remove();
            this.notifications.delete(id);
            this.updateState();
            
            events.emit('notification:removed', id);
        }
    }
    
    /**
     * Remove all notifications
     */
    removeAll() {
        this.notifications.forEach((notification, id) => {
            notification.bsToast.hide();
        });
    }
    
    /**
     * Get Bootstrap color class for notification type
     * @param {string} type - Notification type
     * @returns {string} Bootstrap color class
     */
    getBootstrapColor(type) {
        const colors = {
            success: 'success',
            error: 'danger',
            warning: 'warning',
            info: 'info'
        };
        
        return colors[type] || 'info';
    }
    
    /**
     * Get icon for notification type
     * @param {string} type - Notification type
     * @returns {string} Icon class
     */
    getIcon(type) {
        const icons = {
            success: 'bi-check-circle-fill',
            error: 'bi-exclamation-triangle-fill',
            warning: 'bi-exclamation-triangle-fill',
            info: 'bi-info-circle-fill'
        };
        
        return icons[type] || 'bi-info-circle-fill';
    }
    
    /**
     * Get title for notification type
     * @param {string} type - Notification type
     * @returns {string} Title
     */
    getTitle(type) {
        const titles = {
            success: 'Success',
            error: 'Error',
            warning: 'Warning',
            info: 'Information'
        };
        
        return titles[type] || 'Notification';
    }
    
    /**
     * Generate unique ID
     * @returns {string} Unique ID
     */
    generateId() {
        return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * Update state with current notifications
     */
    updateState() {
        const notificationList = Array.from(this.notifications.entries()).map(([id, notification]) => ({
            id,
            type: notification.config.type,
            message: notification.element.querySelector('.toast-body').textContent
        }));
        
        state.set('ui.notifications', notificationList);
    }
    
    /**
     * Get active notifications
     * @returns {Array} Active notifications
     */
    getActiveNotifications() {
        return Array.from(this.notifications.keys());
    }
    
    /**
     * Check if notification exists
     * @param {string} id - Notification ID
     * @returns {boolean} Whether notification exists
     */
    exists(id) {
        return this.notifications.has(id);
    }
    
    /**
     * Get notification count by type
     * @param {string} type - Notification type
     * @returns {number} Count
     */
    getCountByType(type) {
        let count = 0;
        this.notifications.forEach(notification => {
            if (notification.config.type === type) {
                count++;
            }
        });
        return count;
    }
}

// Create singleton instance
const notificationManager = new NotificationManager();

// Export convenience functions
export function showNotification(message, options) {
    return notificationManager.show(message, options);
}

export function showSuccess(message, options) {
    return notificationManager.success(message, options);
}

export function showError(message, options) {
    return notificationManager.error(message, options);
}

export function showWarning(message, options) {
    return notificationManager.warning(message, options);
}

export function showInfo(message, options) {
    return notificationManager.info(message, options);
}

// Export notification manager for advanced usage
export { notificationManager };
