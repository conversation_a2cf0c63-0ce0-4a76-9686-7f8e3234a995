/**
 * Search Component
 * 
 * Reusable search component with debouncing, filters,
 * and real-time search capabilities.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { events } from '../core/events.js';

export class Search {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        this.options = {
            placeholder: 'Search...',
            debounceDelay: 300,
            minLength: 0,
            showClearButton: true,
            showSearchButton: false,
            filters: [],
            size: '', // 'sm' or 'lg'
            ...options
        };
        
        this.query = '';
        this.activeFilters = new Map();
        this.debounceTimer = null;
        
        this.init();
    }
    
    /**
     * Initialize the search component
     */
    init() {
        if (!this.container) {
            console.error('Search container not found');
            return;
        }
        
        this.render();
        this.setupEventListeners();
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        const input = this.container.querySelector('.search-input');
        const clearBtn = this.container.querySelector('.search-clear');
        const searchBtn = this.container.querySelector('.search-button');
        
        if (input) {
            input.addEventListener('input', (e) => {
                this.handleInput(e.target.value);
            });
            
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.performSearch();
                }
                
                if (e.key === 'Escape') {
                    this.clear();
                }
            });
        }
        
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clear();
            });
        }
        
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.performSearch();
            });
        }
        
        // Filter event listeners
        this.container.addEventListener('change', (e) => {
            if (e.target.classList.contains('search-filter')) {
                this.handleFilterChange(e.target);
            }
        });
    }
    
    /**
     * Handle input changes with debouncing
     * @param {string} value - Input value
     */
    handleInput(value) {
        this.query = value.trim();
        
        // Update clear button visibility
        this.updateClearButton();
        
        // Clear previous timer
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        
        // Set new timer
        this.debounceTimer = setTimeout(() => {
            if (this.query.length >= this.options.minLength) {
                this.performSearch();
            } else if (this.query.length === 0) {
                this.performSearch(); // Allow empty search to show all results
            }
        }, this.options.debounceDelay);
    }
    
    /**
     * Handle filter changes
     * @param {HTMLElement} filterElement - Filter element
     */
    handleFilterChange(filterElement) {
        const filterName = filterElement.getAttribute('data-filter');
        const filterValue = this.getFilterValue(filterElement);
        
        if (filterValue) {
            this.activeFilters.set(filterName, filterValue);
        } else {
            this.activeFilters.delete(filterName);
        }
        
        this.performSearch();
    }
    
    /**
     * Get filter value based on element type
     * @param {HTMLElement} element - Filter element
     * @returns {*} Filter value
     */
    getFilterValue(element) {
        if (element.type === 'checkbox') {
            return element.checked ? element.value : null;
        } else if (element.type === 'radio') {
            return element.checked ? element.value : null;
        } else {
            return element.value || null;
        }
    }
    
    /**
     * Perform search and emit event
     */
    performSearch() {
        const searchData = {
            query: this.query,
            filters: Object.fromEntries(this.activeFilters)
        };
        
        events.emit('search:performed', searchData);
    }
    
    /**
     * Clear search and filters
     */
    clear() {
        this.query = '';
        this.activeFilters.clear();
        
        // Clear input
        const input = this.container.querySelector('.search-input');
        if (input) {
            input.value = '';
        }
        
        // Clear filters
        this.container.querySelectorAll('.search-filter').forEach(filter => {
            if (filter.type === 'checkbox' || filter.type === 'radio') {
                filter.checked = false;
            } else {
                filter.value = '';
            }
        });
        
        this.updateClearButton();
        this.performSearch();
        
        events.emit('search:cleared');
    }
    
    /**
     * Set search query programmatically
     * @param {string} query - Search query
     */
    setQuery(query) {
        this.query = query;
        
        const input = this.container.querySelector('.search-input');
        if (input) {
            input.value = query;
        }
        
        this.updateClearButton();
    }
    
    /**
     * Set filter value programmatically
     * @param {string} filterName - Filter name
     * @param {*} value - Filter value
     */
    setFilter(filterName, value) {
        const filterElement = this.container.querySelector(`[data-filter="${filterName}"]`);
        if (filterElement) {
            if (filterElement.type === 'checkbox') {
                filterElement.checked = Boolean(value);
            } else if (filterElement.type === 'radio') {
                filterElement.checked = filterElement.value === value;
            } else {
                filterElement.value = value;
            }
            
            this.handleFilterChange(filterElement);
        }
    }
    
    /**
     * Update clear button visibility
     */
    updateClearButton() {
        const clearBtn = this.container.querySelector('.search-clear');
        if (clearBtn) {
            clearBtn.style.display = this.query ? 'block' : 'none';
        }
    }
    
    /**
     * Render the search component
     */
    render() {
        const wrapper = document.createElement('div');
        wrapper.className = 'search-component';
        
        // Create input group
        const inputGroup = document.createElement('div');
        inputGroup.className = `input-group${this.options.size ? ` input-group-${this.options.size}` : ''}`;
        
        // Create search input
        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'form-control search-input';
        input.placeholder = this.options.placeholder;
        input.value = this.query;
        
        inputGroup.appendChild(input);
        
        // Create clear button
        if (this.options.showClearButton) {
            const clearBtn = document.createElement('button');
            clearBtn.type = 'button';
            clearBtn.className = 'btn btn-outline-secondary search-clear';
            clearBtn.innerHTML = '<i class="bi bi-x"></i>';
            clearBtn.style.display = this.query ? 'block' : 'none';
            clearBtn.title = 'Clear search';
            
            inputGroup.appendChild(clearBtn);
        }
        
        // Create search button
        if (this.options.showSearchButton) {
            const searchBtn = document.createElement('button');
            searchBtn.type = 'button';
            searchBtn.className = 'btn btn-primary search-button';
            searchBtn.innerHTML = '<i class="bi bi-search"></i>';
            searchBtn.title = 'Search';
            
            inputGroup.appendChild(searchBtn);
        }
        
        wrapper.appendChild(inputGroup);
        
        // Create filters
        if (this.options.filters.length > 0) {
            const filtersContainer = document.createElement('div');
            filtersContainer.className = 'search-filters mt-2';
            
            this.options.filters.forEach(filter => {
                filtersContainer.appendChild(this.renderFilter(filter));
            });
            
            wrapper.appendChild(filtersContainer);
        }
        
        this.container.innerHTML = '';
        this.container.appendChild(wrapper);
    }
    
    /**
     * Render a filter element
     * @param {Object} filter - Filter configuration
     * @returns {HTMLElement} Filter element
     */
    renderFilter(filter) {
        const wrapper = document.createElement('div');
        wrapper.className = 'form-check form-check-inline';
        
        let input;
        
        if (filter.type === 'select') {
            input = document.createElement('select');
            input.className = 'form-select form-select-sm search-filter';
            
            // Add default option
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = filter.placeholder || `All ${filter.label}`;
            input.appendChild(defaultOption);
            
            // Add options
            filter.options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.label;
                input.appendChild(optionElement);
            });
            
            wrapper.appendChild(input);
        } else {
            input = document.createElement('input');
            input.type = filter.type || 'checkbox';
            input.className = 'form-check-input search-filter';
            input.value = filter.value || filter.label;
            input.id = `filter-${filter.name}`;
            
            const label = document.createElement('label');
            label.className = 'form-check-label';
            label.htmlFor = input.id;
            label.textContent = filter.label;
            
            wrapper.appendChild(input);
            wrapper.appendChild(label);
        }
        
        input.setAttribute('data-filter', filter.name);
        
        return wrapper;
    }
    
    /**
     * Get current search state
     * @returns {Object} Current search state
     */
    getState() {
        return {
            query: this.query,
            filters: Object.fromEntries(this.activeFilters)
        };
    }
    
    /**
     * Destroy the search component
     */
    destroy() {
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}
