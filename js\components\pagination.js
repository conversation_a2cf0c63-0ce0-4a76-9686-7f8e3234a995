/**
 * Pagination Component
 * 
 * Reusable pagination component with customizable options
 * and Bootstrap styling support.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { events } from '../core/events.js';

export class Pagination {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        this.options = {
            currentPage: 1,
            totalPages: 1,
            totalItems: 0,
            itemsPerPage: 10,
            maxVisiblePages: 5,
            showInfo: true,
            showFirstLast: true,
            showPrevNext: true,
            size: '', // 'sm' or 'lg' for Bootstrap sizes
            ...options
        };
        
        this.currentPage = this.options.currentPage;
        this.totalPages = this.options.totalPages;
        this.totalItems = this.options.totalItems;
        this.itemsPerPage = this.options.itemsPerPage;
        
        this.init();
    }
    
    /**
     * Initialize the pagination component
     */
    init() {
        if (!this.container) {
            console.error('Pagination container not found');
            return;
        }
        
        this.render();
        this.setupEventListeners();
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        this.container.addEventListener('click', (e) => {
            const pageLink = e.target.closest('[data-page]');
            if (pageLink) {
                e.preventDefault();
                const page = parseInt(pageLink.getAttribute('data-page'));
                this.goToPage(page);
            }
        });
    }
    
    /**
     * Update pagination data
     * @param {Object} data - Pagination data
     */
    update(data) {
        this.currentPage = data.currentPage || this.currentPage;
        this.totalPages = data.totalPages || this.totalPages;
        this.totalItems = data.totalItems || this.totalItems;
        this.itemsPerPage = data.itemsPerPage || this.itemsPerPage;
        
        this.render();
    }
    
    /**
     * Go to specific page
     * @param {number} page - Page number
     */
    goToPage(page) {
        if (page < 1 || page > this.totalPages || page === this.currentPage) {
            return;
        }
        
        this.currentPage = page;
        this.render();
        
        events.emit('pagination:changed', {
            currentPage: this.currentPage,
            totalPages: this.totalPages,
            totalItems: this.totalItems,
            itemsPerPage: this.itemsPerPage
        });
    }
    
    /**
     * Go to next page
     */
    nextPage() {
        this.goToPage(this.currentPage + 1);
    }
    
    /**
     * Go to previous page
     */
    prevPage() {
        this.goToPage(this.currentPage - 1);
    }
    
    /**
     * Go to first page
     */
    firstPage() {
        this.goToPage(1);
    }
    
    /**
     * Go to last page
     */
    lastPage() {
        this.goToPage(this.totalPages);
    }
    
    /**
     * Render the pagination component
     */
    render() {
        if (!this.container) return;
        
        this.container.innerHTML = '';
        
        // Don't show pagination if only one page or no pages
        if (this.totalPages <= 1) {
            if (this.options.showInfo) {
                this.renderInfo();
            }
            return;
        }
        
        const wrapper = document.createElement('div');
        wrapper.className = 'd-flex justify-content-between align-items-center flex-wrap';
        
        // Render pagination info
        if (this.options.showInfo) {
            wrapper.appendChild(this.renderInfo());
        }
        
        // Render pagination controls
        wrapper.appendChild(this.renderControls());
        
        this.container.appendChild(wrapper);
    }
    
    /**
     * Render pagination info
     * @returns {HTMLElement} Info element
     */
    renderInfo() {
        const info = document.createElement('div');
        info.className = 'pagination-info text-muted small';
        
        const start = this.totalItems === 0 ? 0 : (this.currentPage - 1) * this.itemsPerPage + 1;
        const end = Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
        
        info.textContent = `Showing ${start} to ${end} of ${this.totalItems} entries`;
        
        return info;
    }
    
    /**
     * Render pagination controls
     * @returns {HTMLElement} Controls element
     */
    renderControls() {
        const nav = document.createElement('nav');
        nav.setAttribute('aria-label', 'Pagination');
        
        const ul = document.createElement('ul');
        ul.className = `pagination mb-0${this.options.size ? ` pagination-${this.options.size}` : ''}`;
        
        // First page button
        if (this.options.showFirstLast) {
            ul.appendChild(this.createPageItem('First', 1, this.currentPage === 1));
        }
        
        // Previous page button
        if (this.options.showPrevNext) {
            ul.appendChild(this.createPageItem('Previous', this.currentPage - 1, this.currentPage === 1));
        }
        
        // Page number buttons
        const pageNumbers = this.getVisiblePageNumbers();
        pageNumbers.forEach(pageNum => {
            if (pageNum === '...') {
                ul.appendChild(this.createEllipsis());
            } else {
                ul.appendChild(this.createPageItem(pageNum, pageNum, false, pageNum === this.currentPage));
            }
        });
        
        // Next page button
        if (this.options.showPrevNext) {
            ul.appendChild(this.createPageItem('Next', this.currentPage + 1, this.currentPage === this.totalPages));
        }
        
        // Last page button
        if (this.options.showFirstLast) {
            ul.appendChild(this.createPageItem('Last', this.totalPages, this.currentPage === this.totalPages));
        }
        
        nav.appendChild(ul);
        return nav;
    }
    
    /**
     * Create pagination item
     * @param {string} text - Button text
     * @param {number} page - Page number
     * @param {boolean} disabled - Whether button is disabled
     * @param {boolean} active - Whether button is active
     * @returns {HTMLElement} Pagination item
     */
    createPageItem(text, page, disabled = false, active = false) {
        const li = document.createElement('li');
        li.className = `page-item${disabled ? ' disabled' : ''}${active ? ' active' : ''}`;
        
        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        
        if (!disabled) {
            a.setAttribute('data-page', page);
        }
        
        if (active) {
            a.setAttribute('aria-current', 'page');
        }
        
        li.appendChild(a);
        return li;
    }
    
    /**
     * Create ellipsis item
     * @returns {HTMLElement} Ellipsis item
     */
    createEllipsis() {
        const li = document.createElement('li');
        li.className = 'page-item disabled';
        
        const span = document.createElement('span');
        span.className = 'page-link';
        span.textContent = '...';
        
        li.appendChild(span);
        return li;
    }
    
    /**
     * Get visible page numbers with ellipsis
     * @returns {Array} Array of page numbers and ellipsis
     */
    getVisiblePageNumbers() {
        const pages = [];
        const maxVisible = this.options.maxVisiblePages;
        
        if (this.totalPages <= maxVisible) {
            // Show all pages
            for (let i = 1; i <= this.totalPages; i++) {
                pages.push(i);
            }
        } else {
            // Calculate range around current page
            const halfVisible = Math.floor(maxVisible / 2);
            let start = Math.max(1, this.currentPage - halfVisible);
            let end = Math.min(this.totalPages, this.currentPage + halfVisible);
            
            // Adjust if we're near the beginning or end
            if (end - start + 1 < maxVisible) {
                if (start === 1) {
                    end = Math.min(this.totalPages, start + maxVisible - 1);
                } else {
                    start = Math.max(1, end - maxVisible + 1);
                }
            }
            
            // Add first page and ellipsis if needed
            if (start > 1) {
                pages.push(1);
                if (start > 2) {
                    pages.push('...');
                }
            }
            
            // Add visible pages
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            
            // Add ellipsis and last page if needed
            if (end < this.totalPages) {
                if (end < this.totalPages - 1) {
                    pages.push('...');
                }
                pages.push(this.totalPages);
            }
        }
        
        return pages;
    }
    
    /**
     * Get current pagination state
     * @returns {Object} Current state
     */
    getState() {
        return {
            currentPage: this.currentPage,
            totalPages: this.totalPages,
            totalItems: this.totalItems,
            itemsPerPage: this.itemsPerPage
        };
    }
    
    /**
     * Destroy the pagination component
     */
    destroy() {
        if (this.container) {
            this.container.innerHTML = '';
        }
    }
}
