/**
 * Global State Management Module
 * 
 * Provides centralized state management for the application
 * with reactive updates and persistence support.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

class StateManager {
    constructor() {
        this.state = {
            // Application state
            currentPage: 'dashboard',
            theme: 'light',
            
            // Applications page state
            applications: {
                currentPage: 1,
                itemsPerPage: 10,
                totalItems: 0,
                totalPages: 0,
                searchQuery: '',
                selectedRowId: null,
                editingApplicationId: null,
                isLoading: false
            },
            
            // User interface state
            ui: {
                sidebarCollapsed: false,
                notifications: []
            }
        };
        
        this.listeners = new Map();
        this.persistKeys = ['theme', 'applications.itemsPerPage', 'ui.sidebarCollapsed'];
        
        // Load persisted state
        this.loadPersistedState();
    }
    
    /**
     * Get a value from state using dot notation
     * @param {string} path - Dot notation path (e.g., 'applications.currentPage')
     * @returns {*} The value at the specified path
     */
    get(path) {
        return this.getNestedValue(this.state, path);
    }
    
    /**
     * Set a value in state using dot notation
     * @param {string} path - Dot notation path
     * @param {*} value - Value to set
     */
    set(path, value) {
        this.setNestedValue(this.state, path, value);
        this.notifyListeners(path, value);
        this.persistState(path);
    }
    
    /**
     * Update multiple state values at once
     * @param {Object} updates - Object with path-value pairs
     */
    update(updates) {
        Object.entries(updates).forEach(([path, value]) => {
            this.setNestedValue(this.state, path, value);
        });
        
        // Notify all listeners after all updates
        Object.entries(updates).forEach(([path, value]) => {
            this.notifyListeners(path, value);
            this.persistState(path);
        });
    }
    
    /**
     * Subscribe to state changes
     * @param {string} path - Path to watch
     * @param {Function} callback - Callback function
     * @returns {Function} Unsubscribe function
     */
    subscribe(path, callback) {
        if (!this.listeners.has(path)) {
            this.listeners.set(path, new Set());
        }
        
        this.listeners.get(path).add(callback);
        
        // Return unsubscribe function
        return () => {
            const pathListeners = this.listeners.get(path);
            if (pathListeners) {
                pathListeners.delete(callback);
                if (pathListeners.size === 0) {
                    this.listeners.delete(path);
                }
            }
        };
    }
    
    /**
     * Get nested value using dot notation
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }
    
    /**
     * Set nested value using dot notation
     */
    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((current, key) => {
            if (!(key in current)) {
                current[key] = {};
            }
            return current[key];
        }, obj);
        
        target[lastKey] = value;
    }
    
    /**
     * Notify listeners of state changes
     */
    notifyListeners(path, value) {
        // Notify exact path listeners
        const pathListeners = this.listeners.get(path);
        if (pathListeners) {
            pathListeners.forEach(callback => callback(value, path));
        }
        
        // Notify parent path listeners
        const pathParts = path.split('.');
        for (let i = pathParts.length - 1; i > 0; i--) {
            const parentPath = pathParts.slice(0, i).join('.');
            const parentListeners = this.listeners.get(parentPath);
            if (parentListeners) {
                const parentValue = this.get(parentPath);
                parentListeners.forEach(callback => callback(parentValue, parentPath));
            }
        }
    }
    
    /**
     * Load persisted state from localStorage
     */
    loadPersistedState() {
        this.persistKeys.forEach(path => {
            const key = `app_state_${path.replace(/\./g, '_')}`;
            const stored = localStorage.getItem(key);
            if (stored !== null) {
                try {
                    const value = JSON.parse(stored);
                    this.setNestedValue(this.state, path, value);
                } catch (e) {
                    console.warn(`Failed to parse stored state for ${path}:`, e);
                }
            }
        });
    }
    
    /**
     * Persist state to localStorage
     */
    persistState(path) {
        if (this.persistKeys.includes(path)) {
            const key = `app_state_${path.replace(/\./g, '_')}`;
            const value = this.get(path);
            try {
                localStorage.setItem(key, JSON.stringify(value));
            } catch (e) {
                console.warn(`Failed to persist state for ${path}:`, e);
            }
        }
    }
    
    /**
     * Reset state to initial values
     */
    reset() {
        this.state = {
            currentPage: 'dashboard',
            theme: 'light',
            applications: {
                currentPage: 1,
                itemsPerPage: 10,
                totalItems: 0,
                totalPages: 0,
                searchQuery: '',
                selectedRowId: null,
                editingApplicationId: null,
                isLoading: false
            },
            ui: {
                sidebarCollapsed: false,
                notifications: []
            }
        };
        
        // Clear localStorage
        this.persistKeys.forEach(path => {
            const key = `app_state_${path.replace(/\./g, '_')}`;
            localStorage.removeItem(key);
        });
    }
}

// Create and export singleton instance
export const state = new StateManager();
