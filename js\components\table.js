/**
 * Table Component
 * 
 * Reusable table component with sorting, selection, and action support.
 * Provides a consistent interface for displaying tabular data.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { events, EVENT_NAMES } from '../core/events.js';

export class Table {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        this.options = {
            selectable: true,
            sortable: false,
            actions: [],
            emptyMessage: 'No data available',
            rowClass: 'table-row',
            ...options
        };
        
        this.data = [];
        this.columns = [];
        this.selectedRows = new Set();
        this.sortColumn = null;
        this.sortDirection = 'asc';
        
        this.init();
    }
    
    /**
     * Initialize the table
     */
    init() {
        if (!this.container) {
            console.error('Table container not found');
            return;
        }
        
        this.setupEventListeners();
    }
    
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        this.container.addEventListener('click', (e) => {
            this.handleClick(e);
        });
    }
    
    /**
     * Handle click events
     * @param {Event} e - Click event
     */
    handleClick(e) {
        const row = e.target.closest('tr[data-id]');
        const action = e.target.closest('[data-action]');
        
        if (action) {
            e.preventDefault();
            e.stopPropagation();
            this.handleAction(action, row);
        } else if (row && this.options.selectable) {
            this.toggleRowSelection(row);
        }
    }
    
    /**
     * Handle action button clicks
     * @param {HTMLElement} actionElement - Action button element
     * @param {HTMLElement} row - Table row element
     */
    handleAction(actionElement, row) {
        const action = actionElement.getAttribute('data-action');
        const id = row ? row.getAttribute('data-id') : null;
        const rowData = this.getRowData(id);
        
        events.emit(`table:action:${action}`, { id, data: rowData, element: actionElement });
    }
    
    /**
     * Toggle row selection
     * @param {HTMLElement} row - Table row element
     */
    toggleRowSelection(row) {
        const id = row.getAttribute('data-id');
        
        if (this.selectedRows.has(id)) {
            this.selectedRows.delete(id);
            row.classList.remove('table-active');
        } else {
            // Clear previous selection if single select
            if (!this.options.multiSelect) {
                this.clearSelection();
            }
            
            this.selectedRows.add(id);
            row.classList.add('table-active');
        }
        
        events.emit('table:selection:changed', {
            selectedIds: Array.from(this.selectedRows),
            selectedData: this.getSelectedData()
        });
    }
    
    /**
     * Clear all selections
     */
    clearSelection() {
        this.selectedRows.clear();
        this.container.querySelectorAll('tr.table-active').forEach(row => {
            row.classList.remove('table-active');
        });
    }
    
    /**
     * Set table columns
     * @param {Array} columns - Column definitions
     */
    setColumns(columns) {
        this.columns = columns;
    }
    
    /**
     * Set table data
     * @param {Array} data - Table data
     */
    setData(data) {
        this.data = data;
        this.render();
    }
    
    /**
     * Add row to table
     * @param {Object} rowData - Row data
     */
    addRow(rowData) {
        this.data.push(rowData);
        this.render();
    }
    
    /**
     * Update row in table
     * @param {string|number} id - Row ID
     * @param {Object} rowData - Updated row data
     */
    updateRow(id, rowData) {
        const index = this.data.findIndex(row => row.id == id);
        if (index !== -1) {
            this.data[index] = { ...this.data[index], ...rowData };
            this.render();
        }
    }
    
    /**
     * Remove row from table
     * @param {string|number} id - Row ID
     */
    removeRow(id) {
        this.data = this.data.filter(row => row.id != id);
        this.selectedRows.delete(id.toString());
        this.render();
    }
    
    /**
     * Get row data by ID
     * @param {string|number} id - Row ID
     * @returns {Object|null} Row data
     */
    getRowData(id) {
        return this.data.find(row => row.id == id) || null;
    }
    
    /**
     * Get selected row data
     * @returns {Array} Selected row data
     */
    getSelectedData() {
        return this.data.filter(row => this.selectedRows.has(row.id.toString()));
    }
    
    /**
     * Render the table
     */
    render() {
        if (!this.container) return;
        
        // Clear container
        this.container.innerHTML = '';
        
        if (this.data.length === 0) {
            this.renderEmptyState();
            return;
        }
        
        // Create table
        const table = document.createElement('table');
        table.className = 'table table-hover';
        
        // Create header
        if (this.columns.length > 0) {
            table.appendChild(this.renderHeader());
        }
        
        // Create body
        table.appendChild(this.renderBody());
        
        this.container.appendChild(table);
    }
    
    /**
     * Render table header
     * @returns {HTMLElement} Table header element
     */
    renderHeader() {
        const thead = document.createElement('thead');
        const row = document.createElement('tr');
        
        this.columns.forEach(column => {
            const th = document.createElement('th');
            th.textContent = column.title || column.key;
            
            if (column.sortable && this.options.sortable) {
                th.style.cursor = 'pointer';
                th.addEventListener('click', () => this.sort(column.key));
                
                if (this.sortColumn === column.key) {
                    const icon = this.sortDirection === 'asc' ? '↑' : '↓';
                    th.textContent += ` ${icon}`;
                }
            }
            
            if (column.width) {
                th.style.width = column.width;
            }
            
            if (column.className) {
                th.className = column.className;
            }
            
            row.appendChild(th);
        });
        
        thead.appendChild(row);
        return thead;
    }
    
    /**
     * Render table body
     * @returns {HTMLElement} Table body element
     */
    renderBody() {
        const tbody = document.createElement('tbody');
        
        this.data.forEach((rowData, index) => {
            const row = this.renderRow(rowData, index);
            tbody.appendChild(row);
        });
        
        return tbody;
    }
    
    /**
     * Render table row
     * @param {Object} rowData - Row data
     * @param {number} index - Row index
     * @returns {HTMLElement} Table row element
     */
    renderRow(rowData, index) {
        const row = document.createElement('tr');
        row.className = this.options.rowClass;
        row.setAttribute('data-id', rowData.id);
        
        if (this.options.selectable) {
            row.style.cursor = 'pointer';
        }
        
        // Restore selection state
        if (this.selectedRows.has(rowData.id.toString())) {
            row.classList.add('table-active');
        }
        
        this.columns.forEach(column => {
            const td = document.createElement('td');
            
            if (column.render) {
                // Custom render function
                const content = column.render(rowData, index);
                if (typeof content === 'string') {
                    td.innerHTML = content;
                } else {
                    td.appendChild(content);
                }
            } else {
                // Default render
                const value = this.getNestedValue(rowData, column.key);
                td.textContent = value !== null && value !== undefined ? value : '';
            }
            
            if (column.className) {
                td.className = column.className;
            }
            
            row.appendChild(td);
        });
        
        return row;
    }
    
    /**
     * Render empty state
     */
    renderEmptyState() {
        const emptyDiv = document.createElement('div');
        emptyDiv.className = 'text-center py-4 text-muted';
        emptyDiv.innerHTML = `
            <i class="bi bi-inbox fs-1 d-block mb-2"></i>
            <p>${this.options.emptyMessage}</p>
        `;
        this.container.appendChild(emptyDiv);
    }
    
    /**
     * Sort table by column
     * @param {string} columnKey - Column key to sort by
     */
    sort(columnKey) {
        if (this.sortColumn === columnKey) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = columnKey;
            this.sortDirection = 'asc';
        }
        
        this.data.sort((a, b) => {
            const aVal = this.getNestedValue(a, columnKey);
            const bVal = this.getNestedValue(b, columnKey);
            
            let result = 0;
            if (aVal < bVal) result = -1;
            else if (aVal > bVal) result = 1;
            
            return this.sortDirection === 'desc' ? -result : result;
        });
        
        this.render();
        
        events.emit('table:sorted', {
            column: columnKey,
            direction: this.sortDirection
        });
    }
    
    /**
     * Get nested object value
     * @param {Object} obj - Object to get value from
     * @param {string} path - Dot notation path
     * @returns {*} Value at path
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }
    
    /**
     * Destroy the table
     */
    destroy() {
        if (this.container) {
            this.container.innerHTML = '';
        }
        this.data = [];
        this.columns = [];
        this.selectedRows.clear();
    }
}
