<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Pure Bootstrap Admin Template - A clean and modern admin dashboard">
    <meta name="author" content="Admin Panel System">
    <meta name="theme-color" content="#ffffff">

    <title>Pure Bootstrap Admin Template</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

    <!-- Custom CSS for smooth theme transitions -->
    <style>
        .theme-transition,
        .theme-transition *,
        .theme-transition *:before,
        .theme-transition *:after {
            transition: all 300ms !important;
            transition-delay: 0 !important;
        }

        .sidebar {
            min-height: calc(100vh - 56px);
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9998;
        }

        .chart-container {
            position: relative;
            height: 400px;
        }

        @media (max-width: 767.98px) {
            .sidebar {
                min-height: auto;
            }
        }
    </style>
</head>
<body>
    <!-- Loading overlay (hidden by default) -->
    <div id="loading-overlay" class="loading-overlay d-none">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Top Navigation Container -->
    <div id="top-navbar-container"></div>

    <!-- Main Layout -->
    <div class="container-fluid vh-100 d-flex flex-column">
        <div class="row flex-grow-1">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block bg-body-tertiary sidebar collapse" id="left-navbar-container">
                <!-- Navigation will be loaded here -->
            </div>

            <!-- Main Content Area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4" id="main-content">
                <!-- Page content will be loaded here -->
                <div class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Toast Notification Container (will be created by notification manager) -->

    <!-- Scripts -->
    <!-- Bootstrap Bundle (includes Popper) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Main Application Script (New Modular Structure) -->
    <script src="js/core/app.js" type="module"></script>

    <!-- Development Tools (only in development) -->
    <script>
        // Add development helpers
        if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
            console.log('🚀 Pure Bootstrap Admin Template - Development Mode');
            console.log('📁 New Modular Structure Loaded');

            // Make app available globally for debugging
            import('./js/core/app.js').then(module => {
                window.app = module.app;
                window.state = module.state;
                console.log('🔧 Debug tools available: window.app, window.state');
            });
        }
    </script>
</body>
</html>
