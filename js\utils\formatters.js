/**
 * Data Formatters
 * 
 * Utility functions for formatting data for display
 * including dates, numbers, text, and other data types.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { DATE_FORMATS } from './constants.js';

/**
 * Format date for display
 * @param {string|Date} date - Date to format
 * @param {string} format - Format string (default: 'MMM DD, YYYY')
 * @returns {string} Formatted date
 */
export function formatDate(date, format = DATE_FORMATS.DISPLAY) {
    if (!date) return '';
    
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    if (isNaN(dateObj.getTime())) {
        return 'Invalid Date';
    }
    
    // Simple format mapping (can be enhanced with a proper date library)
    const year = dateObj.getFullYear();
    const month = dateObj.getMonth();
    const day = dateObj.getDate();
    const hours = dateObj.getHours();
    const minutes = dateObj.getMinutes();
    const seconds = dateObj.getSeconds();
    
    const monthNames = [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    const monthNamesFull = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    // Replace format tokens
    return format
        .replace('YYYY', year.toString())
        .replace('YY', year.toString().slice(-2))
        .replace('MMMM', monthNamesFull[month])
        .replace('MMM', monthNames[month])
        .replace('MM', (month + 1).toString().padStart(2, '0'))
        .replace('M', (month + 1).toString())
        .replace('DD', day.toString().padStart(2, '0'))
        .replace('D', day.toString())
        .replace('HH', hours.toString().padStart(2, '0'))
        .replace('H', hours.toString())
        .replace('mm', minutes.toString().padStart(2, '0'))
        .replace('m', minutes.toString())
        .replace('ss', seconds.toString().padStart(2, '0'))
        .replace('s', seconds.toString());
}

/**
 * Format date for input fields
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted date (YYYY-MM-DD)
 */
export function formatDateForInput(date) {
    return formatDate(date, DATE_FORMATS.INPUT);
}

/**
 * Format relative time (e.g., "2 hours ago")
 * @param {string|Date} date - Date to format
 * @returns {string} Relative time string
 */
export function formatRelativeTime(date) {
    if (!date) return '';
    
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffMs = now.getTime() - dateObj.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);
    const diffWeeks = Math.floor(diffDays / 7);
    const diffMonths = Math.floor(diffDays / 30);
    const diffYears = Math.floor(diffDays / 365);
    
    if (diffSeconds < 60) {
        return 'just now';
    } else if (diffMinutes < 60) {
        return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
    } else if (diffHours < 24) {
        return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    } else if (diffDays < 7) {
        return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else if (diffWeeks < 4) {
        return `${diffWeeks} week${diffWeeks !== 1 ? 's' : ''} ago`;
    } else if (diffMonths < 12) {
        return `${diffMonths} month${diffMonths !== 1 ? 's' : ''} ago`;
    } else {
        return `${diffYears} year${diffYears !== 1 ? 's' : ''} ago`;
    }
}

/**
 * Format currency
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code (default: 'USD')
 * @param {string} locale - Locale (default: 'en-US')
 * @returns {string} Formatted currency
 */
export function formatCurrency(amount, currency = 'USD', locale = 'en-US') {
    if (typeof amount !== 'number') return amount;
    
    try {
        return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: currency
        }).format(amount);
    } catch (error) {
        console.warn('Currency formatting failed:', error);
        return `${currency} ${amount.toFixed(2)}`;
    }
}

/**
 * Format percentage
 * @param {number} value - Value to format (0-1 or 0-100)
 * @param {number} decimals - Number of decimal places
 * @param {boolean} isDecimal - Whether value is decimal (0-1) or percentage (0-100)
 * @returns {string} Formatted percentage
 */
export function formatPercentage(value, decimals = 1, isDecimal = true) {
    if (typeof value !== 'number') return value;
    
    const percentage = isDecimal ? value * 100 : value;
    return `${percentage.toFixed(decimals)}%`;
}

/**
 * Format file size
 * @param {number} bytes - Size in bytes
 * @param {number} decimals - Number of decimal places
 * @returns {string} Formatted size
 */
export function formatFileSize(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    if (typeof bytes !== 'number') return bytes;
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Format number with locale-specific formatting
 * @param {number} number - Number to format
 * @param {string} locale - Locale (default: 'en-US')
 * @param {Object} options - Intl.NumberFormat options
 * @returns {string} Formatted number
 */
export function formatNumber(number, locale = 'en-US', options = {}) {
    if (typeof number !== 'number') return number;
    
    try {
        return new Intl.NumberFormat(locale, options).format(number);
    } catch (error) {
        console.warn('Number formatting failed:', error);
        return number.toString();
    }
}

/**
 * Format phone number
 * @param {string} phone - Phone number to format
 * @param {string} format - Format pattern (default: US format)
 * @returns {string} Formatted phone number
 */
export function formatPhoneNumber(phone, format = 'US') {
    if (!phone || typeof phone !== 'string') return phone;
    
    // Remove all non-digit characters
    const digits = phone.replace(/\D/g, '');
    
    if (format === 'US') {
        if (digits.length === 10) {
            return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
        } else if (digits.length === 11 && digits[0] === '1') {
            return `+1 (${digits.slice(1, 4)}) ${digits.slice(4, 7)}-${digits.slice(7)}`;
        }
    }
    
    return phone; // Return original if can't format
}

/**
 * Format text for display (truncate, capitalize, etc.)
 * @param {string} text - Text to format
 * @param {Object} options - Formatting options
 * @returns {string} Formatted text
 */
export function formatText(text, options = {}) {
    if (!text || typeof text !== 'string') return text;
    
    const {
        maxLength = null,
        capitalize = false,
        titleCase = false,
        uppercase = false,
        lowercase = false,
        trim = true,
        suffix = '...'
    } = options;
    
    let formatted = trim ? text.trim() : text;
    
    if (maxLength && formatted.length > maxLength) {
        formatted = formatted.substring(0, maxLength - suffix.length) + suffix;
    }
    
    if (uppercase) {
        formatted = formatted.toUpperCase();
    } else if (lowercase) {
        formatted = formatted.toLowerCase();
    } else if (titleCase) {
        formatted = formatted.replace(/\w\S*/g, (txt) => 
            txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
        );
    } else if (capitalize) {
        formatted = formatted.charAt(0).toUpperCase() + formatted.slice(1);
    }
    
    return formatted;
}

/**
 * Format application type for display
 * @param {string} type - Application type
 * @returns {string} Formatted type
 */
export function formatApplicationType(type) {
    if (!type) return '';
    
    const typeMap = {
        'web': 'Web Application',
        'mobile': 'Mobile App',
        'desktop': 'Desktop App',
        'api': 'API Service',
        'library': 'Library',
        'framework': 'Framework',
        'tool': 'Development Tool',
        'other': 'Other'
    };
    
    return typeMap[type.toLowerCase()] || formatText(type, { titleCase: true });
}

/**
 * Format version number
 * @param {string} version - Version string
 * @returns {string} Formatted version
 */
export function formatVersion(version) {
    if (!version || typeof version !== 'string') return version;
    
    // Add 'v' prefix if not present
    return version.startsWith('v') ? version : `v${version}`;
}

/**
 * Format status for display
 * @param {string} status - Status value
 * @param {Object} options - Formatting options
 * @returns {string} Formatted status with badge
 */
export function formatStatus(status, options = {}) {
    if (!status) return '';
    
    const {
        showBadge = true,
        badgeClass = 'badge'
    } = options;
    
    const statusMap = {
        'active': { text: 'Active', class: 'bg-success' },
        'inactive': { text: 'Inactive', class: 'bg-secondary' },
        'pending': { text: 'Pending', class: 'bg-warning' },
        'error': { text: 'Error', class: 'bg-danger' },
        'success': { text: 'Success', class: 'bg-success' },
        'warning': { text: 'Warning', class: 'bg-warning' },
        'info': { text: 'Info', class: 'bg-info' }
    };
    
    const statusInfo = statusMap[status.toLowerCase()] || {
        text: formatText(status, { titleCase: true }),
        class: 'bg-secondary'
    };
    
    if (showBadge) {
        return `<span class="${badgeClass} ${statusInfo.class}">${statusInfo.text}</span>`;
    }
    
    return statusInfo.text;
}

/**
 * Format boolean value for display
 * @param {boolean} value - Boolean value
 * @param {Object} options - Formatting options
 * @returns {string} Formatted boolean
 */
export function formatBoolean(value, options = {}) {
    const {
        trueText = 'Yes',
        falseText = 'No',
        showIcon = false,
        trueIcon = 'bi-check-circle-fill text-success',
        falseIcon = 'bi-x-circle-fill text-danger'
    } = options;
    
    const text = value ? trueText : falseText;
    
    if (showIcon) {
        const icon = value ? trueIcon : falseIcon;
        return `<i class="bi ${icon} me-1"></i>${text}`;
    }
    
    return text;
}

/**
 * Format array for display
 * @param {Array} array - Array to format
 * @param {Object} options - Formatting options
 * @returns {string} Formatted array
 */
export function formatArray(array, options = {}) {
    if (!Array.isArray(array)) return array;
    
    const {
        separator = ', ',
        maxItems = null,
        moreText = 'more'
    } = options;
    
    let items = array;
    
    if (maxItems && array.length > maxItems) {
        items = array.slice(0, maxItems);
        const remaining = array.length - maxItems;
        items.push(`+${remaining} ${moreText}`);
    }
    
    return items.join(separator);
}

/**
 * Format object for display
 * @param {Object} obj - Object to format
 * @param {Object} options - Formatting options
 * @returns {string} Formatted object
 */
export function formatObject(obj, options = {}) {
    if (!obj || typeof obj !== 'object') return obj;
    
    const {
        keyValueSeparator = ': ',
        itemSeparator = ', ',
        maxItems = null
    } = options;
    
    const entries = Object.entries(obj);
    let items = entries.map(([key, value]) => `${key}${keyValueSeparator}${value}`);
    
    if (maxItems && items.length > maxItems) {
        items = items.slice(0, maxItems);
        const remaining = entries.length - maxItems;
        items.push(`+${remaining} more`);
    }
    
    return items.join(itemSeparator);
}
