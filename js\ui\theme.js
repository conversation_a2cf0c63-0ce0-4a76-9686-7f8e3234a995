/**
 * Theme Management Module
 * 
 * Enhanced theme management with state integration,
 * system preference detection, and smooth transitions.
 * 
 * <AUTHOR> Panel System
 * @version 1.0.0
 */

import { events, EVENT_NAMES } from '../core/events.js';
import { state } from '../core/state.js';
import { storage } from '../services/storage.js';

class ThemeManager {
    constructor() {
        this.themes = ['light', 'dark', 'auto'];
        this.currentTheme = 'light';
        this.systemTheme = 'light';
        this.initialized = false;
        
        // Detect system theme preference
        this.detectSystemTheme();
        this.setupSystemThemeListener();
    }
    
    /**
     * Initialize theme management
     */
    initialize() {
        if (this.initialized) {
            console.log('Theme manager already initialized');
            return;
        }
        
        try {
            // Load saved theme or use default
            this.loadSavedTheme();
            
            // Set up theme controls
            this.setupThemeControls();
            
            // Apply initial theme
            this.applyTheme(this.currentTheme);
            
            this.initialized = true;
            
            events.emit(EVENT_NAMES.THEME_CHANGED, this.currentTheme);
            
            console.log('Theme manager initialized');
            
        } catch (error) {
            console.error('Failed to initialize theme manager:', error);
        }
    }
    
    /**
     * Detect system theme preference
     */
    detectSystemTheme() {
        if (window.matchMedia) {
            const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
            this.systemTheme = darkModeQuery.matches ? 'dark' : 'light';
        }
    }
    
    /**
     * Set up system theme change listener
     */
    setupSystemThemeListener() {
        if (window.matchMedia) {
            const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
            darkModeQuery.addEventListener('change', (e) => {
                this.systemTheme = e.matches ? 'dark' : 'light';
                
                // If current theme is auto, apply the new system theme
                if (this.currentTheme === 'auto') {
                    this.applyTheme('auto');
                }
            });
        }
    }
    
    /**
     * Load saved theme from storage
     */
    loadSavedTheme() {
        const savedTheme = storage.getLocal('theme', 'light');
        
        if (this.themes.includes(savedTheme)) {
            this.currentTheme = savedTheme;
        } else {
            this.currentTheme = 'light';
        }
        
        // Update state
        state.set('theme', this.currentTheme);
    }
    
    /**
     * Set up theme control event listeners
     */
    setupThemeControls() {
        // Theme dropdown items
        document.addEventListener('click', (e) => {
            const themeItem = e.target.closest('[data-theme]');
            if (themeItem) {
                e.preventDefault();
                const theme = themeItem.getAttribute('data-theme');
                this.setTheme(theme);
            }
        });
        
        // Theme toggle button (if exists)
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
    }
    
    /**
     * Set theme
     * @param {string} theme - Theme name ('light', 'dark', 'auto')
     */
    setTheme(theme) {
        if (!this.themes.includes(theme)) {
            console.warn(`Unknown theme: ${theme}`);
            return;
        }
        
        this.currentTheme = theme;
        
        // Save to storage
        storage.setLocal('theme', theme);
        
        // Update state
        state.set('theme', theme);
        
        // Apply theme
        this.applyTheme(theme);
        
        // Update UI controls
        this.updateThemeControls();
        
        // Emit event
        events.emit(EVENT_NAMES.THEME_CHANGED, theme);
        
        console.log(`Theme changed to: ${theme}`);
    }
    
    /**
     * Apply theme to document
     * @param {string} theme - Theme name
     */
    applyTheme(theme) {
        let effectiveTheme = theme;
        
        // Resolve 'auto' theme to actual theme
        if (theme === 'auto') {
            effectiveTheme = this.systemTheme;
        }
        
        // Apply theme to document
        document.documentElement.setAttribute('data-bs-theme', effectiveTheme);
        
        // Add transition class for smooth theme changes
        document.documentElement.classList.add('theme-transition');
        
        // Remove transition class after animation
        setTimeout(() => {
            document.documentElement.classList.remove('theme-transition');
        }, 300);
        
        // Update meta theme-color for mobile browsers
        this.updateMetaThemeColor(effectiveTheme);
    }
    
    /**
     * Update meta theme-color for mobile browsers
     * @param {string} theme - Effective theme
     */
    updateMetaThemeColor(theme) {
        let themeColor = '#ffffff'; // Default light theme color
        
        if (theme === 'dark') {
            themeColor = '#212529'; // Dark theme color
        }
        
        // Update existing meta tag or create new one
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        
        metaThemeColor.content = themeColor;
    }
    
    /**
     * Toggle between light and dark themes
     */
    toggleTheme() {
        const nextTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(nextTheme);
    }
    
    /**
     * Update theme control UI elements
     */
    updateThemeControls() {
        // Update dropdown items
        document.querySelectorAll('[data-theme]').forEach(item => {
            const itemTheme = item.getAttribute('data-theme');
            
            if (itemTheme === this.currentTheme) {
                item.classList.add('active');
                
                // Update dropdown button text if it exists
                const dropdownButton = document.getElementById('themeDropdown');
                if (dropdownButton) {
                    const icon = this.getThemeIcon(this.currentTheme);
                    const label = this.getThemeLabel(this.currentTheme);
                    dropdownButton.innerHTML = `${icon} ${label}`;
                }
            } else {
                item.classList.remove('active');
            }
        });
        
        // Update toggle button icon
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            const icon = this.getThemeIcon(this.currentTheme);
            themeToggle.innerHTML = icon;
        }
    }
    
    /**
     * Get theme icon
     * @param {string} theme - Theme name
     * @returns {string} Icon HTML
     */
    getThemeIcon(theme) {
        const icons = {
            light: '<i class="bi bi-sun-fill"></i>',
            dark: '<i class="bi bi-moon-fill"></i>',
            auto: '<i class="bi bi-circle-half"></i>'
        };
        
        return icons[theme] || icons.light;
    }
    
    /**
     * Get theme label
     * @param {string} theme - Theme name
     * @returns {string} Theme label
     */
    getThemeLabel(theme) {
        const labels = {
            light: 'Light',
            dark: 'Dark',
            auto: 'Auto'
        };
        
        return labels[theme] || labels.light;
    }
    
    /**
     * Get current theme
     * @returns {string} Current theme
     */
    getCurrentTheme() {
        return this.currentTheme;
    }
    
    /**
     * Get effective theme (resolves 'auto')
     * @returns {string} Effective theme
     */
    getEffectiveTheme() {
        return this.currentTheme === 'auto' ? this.systemTheme : this.currentTheme;
    }
    
    /**
     * Get system theme preference
     * @returns {string} System theme
     */
    getSystemTheme() {
        return this.systemTheme;
    }
    
    /**
     * Check if theme is supported
     * @param {string} theme - Theme name
     * @returns {boolean} Whether theme is supported
     */
    isThemeSupported(theme) {
        return this.themes.includes(theme);
    }
    
    /**
     * Get available themes
     * @returns {Array} Available themes
     */
    getAvailableThemes() {
        return [...this.themes];
    }
}

// Create singleton instance
const themeManager = new ThemeManager();

// Export initialization function for compatibility
export function initializeTheme() {
    themeManager.initialize();
}

// Export theme manager for advanced usage
export { themeManager };
